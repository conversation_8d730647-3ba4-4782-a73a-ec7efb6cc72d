/**
 * FinScanAI User Model
 * Handles user authentication and profile management
 */

const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');
const config = require('../config/environment');

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false
  },
  
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
    validate: {
      isEmail: {
        msg: 'Please provide a valid email address'
      },
      len: {
        args: [5, 255],
        msg: 'Email must be between 5 and 255 characters'
      }
    }
  },
  
  password: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: {
        args: [8, 255],
        msg: 'Password must be at least 8 characters long'
      }
    }
  },
  
  firstName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [1, 50],
        msg: 'First name must be between 1 and 50 characters'
      }
    }
  },
  
  lastName: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      len: {
        args: [1, 50],
        msg: 'Last name must be between 1 and 50 characters'
      }
    }
  },
  
  role: {
    type: DataTypes.ENUM('user', 'admin', 'premium'),
    defaultValue: 'user',
    allowNull: false
  },
  
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    allowNull: false
  },
  
  isEmailVerified: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  
  emailVerificationToken: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  passwordResetToken: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  passwordResetExpires: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  loginAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  
  lockUntil: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  // Usage tracking
  documentsProcessed: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  
  monthlyUsage: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  
  usageResetDate: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  },
  
  // Preferences
  preferences: {
    type: DataTypes.JSON,
    defaultValue: {
      language: 'en',
      defaultExportFormat: 'excel',
      emailNotifications: true,
      theme: 'light'
    }
  }
}, {
  tableName: 'users',
  timestamps: true,
  paranoid: true, // Soft delete
  indexes: [
    {
      unique: true,
      fields: ['email']
    },
    {
      fields: ['role']
    },
    {
      fields: ['isActive']
    },
    {
      fields: ['createdAt']
    }
  ],
  hooks: {
    beforeCreate: async (user) => {
      if (user.password) {
        user.password = await bcrypt.hash(user.password, config.auth.bcryptRounds);
      }
    },
    beforeUpdate: async (user) => {
      if (user.changed('password')) {
        user.password = await bcrypt.hash(user.password, config.auth.bcryptRounds);
      }
    }
  }
});

/**
 * Instance Methods
 */

// Compare password
User.prototype.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Check if account is locked
User.prototype.isLocked = function() {
  return !!(this.lockUntil && this.lockUntil > Date.now());
};

// Increment login attempts
User.prototype.incLoginAttempts = async function() {
  const maxAttempts = 5;
  const lockTime = 2 * 60 * 60 * 1000; // 2 hours

  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < Date.now()) {
    return this.update({
      loginAttempts: 1,
      lockUntil: null
    });
  }

  const updates = { loginAttempts: this.loginAttempts + 1 };

  // Lock account after max attempts
  if (updates.loginAttempts >= maxAttempts && !this.isLocked()) {
    updates.lockUntil = Date.now() + lockTime;
  }

  return this.update(updates);
};

// Reset login attempts
User.prototype.resetLoginAttempts = async function() {
  return this.update({
    loginAttempts: 0,
    lockUntil: null,
    lastLoginAt: new Date()
  });
};

// Get full name
User.prototype.getFullName = function() {
  return `${this.firstName || ''} ${this.lastName || ''}`.trim() || this.email;
};

// Check usage limits
User.prototype.canProcessDocument = function() {
  const limits = {
    user: 50,
    premium: 500,
    admin: Infinity
  };
  
  return this.monthlyUsage < limits[this.role];
};

// Increment usage
User.prototype.incrementUsage = async function() {
  // Reset monthly usage if needed
  const now = new Date();
  const resetDate = new Date(this.usageResetDate);
  
  if (now.getMonth() !== resetDate.getMonth() || now.getFullYear() !== resetDate.getFullYear()) {
    await this.update({
      monthlyUsage: 1,
      documentsProcessed: this.documentsProcessed + 1,
      usageResetDate: now
    });
  } else {
    await this.update({
      monthlyUsage: this.monthlyUsage + 1,
      documentsProcessed: this.documentsProcessed + 1
    });
  }
};

/**
 * Class Methods
 */

// Find by email
User.findByEmail = function(email) {
  return this.findOne({ where: { email: email.toLowerCase() } });
};

// Create user with validation
User.createUser = async function(userData) {
  const user = await this.create({
    ...userData,
    email: userData.email.toLowerCase()
  });
  
  // Remove password from response
  const userResponse = user.toJSON();
  delete userResponse.password;
  return userResponse;
};

module.exports = User;
