/**
 * FinScanAI Frontend Application
 * Main React application component with routing and global state management
 */

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { Toaster } from 'react-hot-toast';

// Components
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import LoadingSpinner from './components/LoadingSpinner';

// Pages
import Dashboard from './pages/Dashboard';
import Upload from './pages/Upload';
import Results from './pages/Results';
import Login from './pages/Login';
import Register from './pages/Register';
import Profile from './pages/Profile';

// Services
import { authService } from './services/api';

// Styles
import './styles/main.css';

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [theme, setTheme] = useState('light');

  // Initialize app
  useEffect(() => {
    initializeApp();
  }, []);

  // Theme management
  useEffect(() => {
    const savedTheme = localStorage.getItem('finscannai-theme') || 'light';
    setTheme(savedTheme);
    document.documentElement.setAttribute('data-theme', savedTheme);
  }, []);

  const initializeApp = async () => {
    try {
      // Check if user is logged in
      const token = localStorage.getItem('finscannai-token');
      if (token) {
        const userData = await authService.getProfile();
        setUser(userData.user);
      }
    } catch (error) {
      console.error('Failed to initialize app:', error);
      // Clear invalid token
      localStorage.removeItem('finscannai-token');
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = (userData, token) => {
    setUser(userData);
    localStorage.setItem('finscannai-token', token);
  };

  const handleLogout = () => {
    setUser(null);
    localStorage.removeItem('finscannai-token');
    queryClient.clear(); // Clear all cached data
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('finscannai-theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Protected Route Component
  const ProtectedRoute = ({ children }) => {
    if (loading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="large" />
        </div>
      );
    }

    if (!user) {
      return <Navigate to="/login" replace />;
    }

    return children;
  };

  // Public Route Component (redirect if logged in)
  const PublicRoute = ({ children }) => {
    if (loading) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <LoadingSpinner size="large" />
        </div>
      );
    }

    if (user) {
      return <Navigate to="/dashboard" replace />;
    }

    return children;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <h2 className="mt-4 text-xl font-semibold text-gray-700">
            Loading FinScanAI...
          </h2>
          <p className="mt-2 text-gray-500">
            Preparing your financial OCR solution
          </p>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <Router>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'var(--surface)',
                color: 'var(--text-primary)',
                border: '1px solid var(--border)',
              },
              success: {
                iconTheme: {
                  primary: 'var(--success)',
                  secondary: 'white',
                },
              },
              error: {
                iconTheme: {
                  primary: 'var(--error)',
                  secondary: 'white',
                },
              },
            }}
          />

          <Routes>
            {/* Public Routes */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <Login onLogin={handleLogin} />
                </PublicRoute>
              }
            />
            <Route
              path="/register"
              element={
                <PublicRoute>
                  <Register onLogin={handleLogin} />
                </PublicRoute>
              }
            />

            {/* Protected Routes */}
            <Route
              path="/*"
              element={
                <ProtectedRoute>
                  <div className="flex h-screen overflow-hidden">
                    {/* Sidebar */}
                    <Sidebar
                      open={sidebarOpen}
                      onClose={() => setSidebarOpen(false)}
                      user={user}
                    />

                    {/* Main Content */}
                    <div className="flex-1 flex flex-col overflow-hidden">
                      {/* Navbar */}
                      <Navbar
                        user={user}
                        onToggleSidebar={toggleSidebar}
                        onToggleTheme={toggleTheme}
                        onLogout={handleLogout}
                        theme={theme}
                      />

                      {/* Page Content */}
                      <main className="flex-1 overflow-auto">
                        <Routes>
                          <Route path="/" element={<Navigate to="/dashboard" replace />} />
                          <Route path="/dashboard" element={<Dashboard user={user} />} />
                          <Route path="/upload" element={<Upload user={user} />} />
                          <Route path="/results" element={<Results user={user} />} />
                          <Route path="/results/:id" element={<Results user={user} />} />
                          <Route path="/profile" element={<Profile user={user} onUserUpdate={setUser} />} />
                          
                          {/* 404 Route */}
                          <Route
                            path="*"
                            element={
                              <div className="min-h-full flex items-center justify-center">
                                <div className="text-center">
                                  <h1 className="text-6xl font-bold text-gray-400">404</h1>
                                  <h2 className="mt-4 text-2xl font-semibold text-gray-700 dark:text-gray-300">
                                    Page Not Found
                                  </h2>
                                  <p className="mt-2 text-gray-500 dark:text-gray-400">
                                    The page you're looking for doesn't exist.
                                  </p>
                                  <button
                                    onClick={() => window.history.back()}
                                    className="mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                  >
                                    Go Back
                                  </button>
                                </div>
                              </div>
                            }
                          />
                        </Routes>
                      </main>
                    </div>
                  </div>
                </ProtectedRoute>
              }
            />
          </Routes>
        </div>
      </Router>
    </QueryClientProvider>
  );
}

export default App;
