"""
FinScanAI Image Preprocessor
Advanced image preprocessing for optimal OCR results
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
from typing import Tuple, Optional, Union
import logging
from pathlib import Path

from ..config.settings import settings

logger = logging.getLogger(__name__)


class ImagePreprocessor:
    """Advanced image preprocessing for financial documents"""
    
    def __init__(self):
        self.target_dpi = settings.image_dpi
        self.max_width = settings.image_max_width
        self.max_height = settings.image_max_height
        
    def preprocess_image(self, image_path: Union[str, Path]) -> np.ndarray:
        """
        Main preprocessing pipeline for financial documents
        
        Args:
            image_path: Path to the input image
            
        Returns:
            Preprocessed image as numpy array
        """
        try:
            # Load image
            image = self._load_image(image_path)
            
            # Apply preprocessing pipeline
            if settings.enhance_image:
                image = self._enhance_image(image)
                image = self._denoise_image(image)
                image = self._correct_skew(image)
                image = self._normalize_lighting(image)
                image = self._sharpen_image(image)
                image = self._binarize_image(image)
            
            # Resize if necessary
            image = self._resize_image(image)
            
            logger.info(f"Successfully preprocessed image: {image_path}")
            return image
            
        except Exception as e:
            logger.error(f"Error preprocessing image {image_path}: {str(e)}")
            raise
    
    def _load_image(self, image_path: Union[str, Path]) -> np.ndarray:
        """Load image from file"""
        image_path = Path(image_path)
        
        if not image_path.exists():
            raise FileNotFoundError(f"Image file not found: {image_path}")
        
        # Load with OpenCV
        image = cv2.imread(str(image_path))
        
        if image is None:
            # Try with PIL as fallback
            pil_image = Image.open(image_path)
            image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        if image is None:
            raise ValueError(f"Unable to load image: {image_path}")
        
        return image
    
    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """Enhance image contrast and brightness"""
        # Convert to PIL for enhancement
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # Enhance contrast
        enhancer = ImageEnhance.Contrast(pil_image)
        pil_image = enhancer.enhance(1.2)
        
        # Enhance brightness
        enhancer = ImageEnhance.Brightness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Enhance sharpness
        enhancer = ImageEnhance.Sharpness(pil_image)
        pil_image = enhancer.enhance(1.1)
        
        # Convert back to OpenCV format
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
    
    def _denoise_image(self, image: np.ndarray) -> np.ndarray:
        """Remove noise from image"""
        # Apply Non-local Means Denoising
        if len(image.shape) == 3:
            denoised = cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
        else:
            denoised = cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
        
        return denoised
    
    def _correct_skew(self, image: np.ndarray) -> np.ndarray:
        """Correct skew/rotation in the image"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply edge detection
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Detect lines using Hough transform
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None:
            # Calculate average angle
            angles = []
            for rho, theta in lines[:10]:  # Use first 10 lines
                angle = theta * 180 / np.pi
                if angle < 45:
                    angles.append(angle)
                elif angle > 135:
                    angles.append(angle - 180)
            
            if angles:
                median_angle = np.median(angles)
                
                # Rotate image if skew is significant
                if abs(median_angle) > 0.5:
                    return self._rotate_image(image, -median_angle)
        
        return image
    
    def _rotate_image(self, image: np.ndarray, angle: float) -> np.ndarray:
        """Rotate image by given angle"""
        height, width = image.shape[:2]
        center = (width // 2, height // 2)
        
        # Get rotation matrix
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # Calculate new dimensions
        cos_angle = abs(rotation_matrix[0, 0])
        sin_angle = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_angle) + (width * cos_angle))
        new_height = int((height * cos_angle) + (width * sin_angle))
        
        # Adjust rotation matrix for new center
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]
        
        # Perform rotation
        rotated = cv2.warpAffine(image, rotation_matrix, (new_width, new_height), 
                                flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
        
        return rotated
    
    def _normalize_lighting(self, image: np.ndarray) -> np.ndarray:
        """Normalize lighting conditions"""
        # Convert to LAB color space
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        
        # Split channels
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge channels
        lab = cv2.merge([l, a, b])
        
        # Convert back to BGR
        normalized = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        
        return normalized
    
    def _sharpen_image(self, image: np.ndarray) -> np.ndarray:
        """Apply sharpening filter"""
        # Define sharpening kernel
        kernel = np.array([[-1, -1, -1],
                          [-1,  9, -1],
                          [-1, -1, -1]])
        
        # Apply kernel
        sharpened = cv2.filter2D(image, -1, kernel)
        
        return sharpened
    
    def _binarize_image(self, image: np.ndarray) -> np.ndarray:
        """Convert to binary image for better OCR"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Apply adaptive threshold
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # Convert back to 3-channel for consistency
        binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
        
        return binary_bgr
    
    def _resize_image(self, image: np.ndarray) -> np.ndarray:
        """Resize image if it exceeds maximum dimensions"""
        height, width = image.shape[:2]
        
        if width <= self.max_width and height <= self.max_height:
            return image
        
        # Calculate scaling factor
        scale_w = self.max_width / width
        scale_h = self.max_height / height
        scale = min(scale_w, scale_h)
        
        # Calculate new dimensions
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        logger.info(f"Resized image from {width}x{height} to {new_width}x{new_height}")
        
        return resized
    
    def preprocess_for_table_detection(self, image: np.ndarray) -> np.ndarray:
        """Specialized preprocessing for table detection"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply morphological operations to enhance table structure
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        # Closing operation to connect broken lines
        closed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
        
        # Opening operation to remove noise
        opened = cv2.morphologyEx(closed, cv2.MORPH_OPEN, kernel)
        
        return opened
    
    def extract_text_regions(self, image: np.ndarray) -> list:
        """Extract potential text regions from image"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply MSER (Maximally Stable Extremal Regions)
        mser = cv2.MSER_create()
        regions, _ = mser.detectRegions(gray)
        
        # Convert regions to bounding boxes
        bboxes = []
        for region in regions:
            x, y, w, h = cv2.boundingRect(region.reshape(-1, 1, 2))
            bboxes.append((x, y, w, h))
        
        return bboxes
    
    def save_preprocessed_image(self, image: np.ndarray, output_path: Union[str, Path]) -> None:
        """Save preprocessed image to file"""
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        success = cv2.imwrite(str(output_path), image)
        
        if not success:
            raise ValueError(f"Failed to save image to {output_path}")
        
        logger.info(f"Saved preprocessed image to {output_path}")


# Global preprocessor instance
preprocessor = ImagePreprocessor()


def preprocess_image(image_path: Union[str, Path]) -> np.ndarray:
    """Convenience function for image preprocessing"""
    return preprocessor.preprocess_image(image_path)
