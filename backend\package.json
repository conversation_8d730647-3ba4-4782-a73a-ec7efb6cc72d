{"name": "finscannai-backend", "version": "1.0.0", "description": "FinScanAI Backend API - Node.js/Express server for Financial Report OCR", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "migrate": "node src/utils/migrate.js", "seed": "node src/utils/seed.js", "build": "echo 'No build step required for Node.js'", "docker:build": "docker build -t finscannai-backend .", "docker:run": "docker run -p 5000:5000 finscannai-backend"}, "keywords": ["finscannai", "ocr", "financial", "api", "express", "nodejs"], "author": "HectorTa1989", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "sqlite3": "^5.1.6", "pg": "^8.11.3", "sequelize": "^6.35.2", "axios": "^1.6.2", "sharp": "^0.33.1", "pdf-parse": "^1.1.1", "xlsx": "^0.18.5", "csv-writer": "^1.6.0", "uuid": "^9.0.1", "joi": "^17.11.0", "winston": "^3.11.0", "express-winston": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-node": "^11.1.0", "prettier": "^3.1.1", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/*.js"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"env": {"node": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}}}