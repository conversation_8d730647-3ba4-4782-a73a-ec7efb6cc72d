# FinScanAI OCR Service Dependencies
# Core OCR and Image Processing
pytesseract==0.3.10
opencv-python==********
Pillow==10.1.0
numpy==1.24.3
scipy==1.11.4

# PDF Processing
PyMuPDF==1.23.8
pdf2image==1.16.3
pypdf==3.0.1

# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Data Processing
pandas==2.1.4
openpyxl==3.1.2
xlsxwriter==3.1.9

# Machine Learning & AI
scikit-learn==1.3.2
scikit-image==0.22.0
tensorflow==2.15.0
torch==2.1.1
torchvision==0.16.1

# Text Processing
nltk==3.8.1
spacy==3.7.2
textdistance==4.6.0
fuzzywuzzy==0.18.0
python-levenshtein==0.23.0

# Financial Data Processing
regex==2023.10.3
dateparser==1.2.0
babel==2.13.1

# Google Vision API (Optional)
google-cloud-vision==3.4.5

# Utilities
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0
requests==2.31.0
aiofiles==23.2.0
asyncio==3.4.3

# Logging and Monitoring
loguru==0.7.2
prometheus-client==0.19.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Image Enhancement
imageio==2.33.0
matplotlib==3.8.2
seaborn==0.13.0

# Database (Optional)
sqlalchemy==2.0.23
psycopg2-binary==2.9.9

# Caching
redis==5.0.1

# File Format Support
python-magic==0.4.27
filetype==1.2.0

# Concurrent Processing
celery==5.3.4
kombu==5.3.4
