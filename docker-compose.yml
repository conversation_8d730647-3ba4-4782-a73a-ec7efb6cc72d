version: '3.8'

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: finscannai-database
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-finscannai}
      POSTGRES_USER: ${POSTGRES_USER:-finscannai_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - finscannai-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-finscannai_user}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache (Optional)
  redis:
    image: redis:7-alpine
    container_name: finscannai-redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - finscannai-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # OCR Service (Python)
  ocr-service:
    build:
      context: ./ocr-service
      dockerfile: Dockerfile
    container_name: finscannai-ocr-service
    environment:
      - TESSERACT_PATH=/usr/bin/tesseract
      - SUPPORTED_LANGUAGES=eng+vie
      - MAX_FILE_SIZE=10485760
      - GOOGLE_VISION_API_KEY=${GOOGLE_VISION_API_KEY}
    ports:
      - "${OCR_SERVICE_PORT:-8000}:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    networks:
      - finscannai-network
    restart: unless-stopped
    depends_on:
      - database
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Node.js)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: finscannai-backend
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DATABASE_URL=postgresql://${POSTGRES_USER:-finscannai_user}:${POSTGRES_PASSWORD:-secure_password}@database:5432/${POSTGRES_DB:-finscannai}
      - REDIS_URL=redis://redis:6379
      - OCR_SERVICE_URL=http://ocr-service:8000
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-10485760}
    ports:
      - "${BACKEND_PORT:-5000}:5000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - finscannai-network
    restart: unless-stopped
    depends_on:
      - database
      - redis
      - ocr-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React.js)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=http://localhost:${BACKEND_PORT:-5000}/api
        - REACT_APP_OCR_SERVICE_URL=http://localhost:${OCR_SERVICE_PORT:-8000}
    container_name: finscannai-frontend
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    networks:
      - finscannai-network
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: finscannai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    networks:
      - finscannai-network
    restart: unless-stopped
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  finscannai-network:
    driver: bridge
