"""
FinScanAI Text Extractor
Advanced OCR text extraction with multiple engines and confidence scoring
"""

import pytesseract
import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple, Union
import logging
import re
from pathlib import Path
import json

from ..config.settings import settings, TESSERACT_CONFIG
from .image_preprocessor import preprocessor

# Optional Google Vision API
try:
    from google.cloud import vision
    GOOGLE_VISION_AVAILABLE = True
except ImportError:
    GOOGLE_VISION_AVAILABLE = False

logger = logging.getLogger(__name__)


class TextExtractor:
    """Advanced text extraction with multiple OCR engines"""
    
    def __init__(self):
        self.tesseract_config = TESSERACT_CONFIG
        self.confidence_threshold = settings.ocr_confidence_threshold
        
        # Initialize Google Vision client if available
        self.vision_client = None
        if GOOGLE_VISION_AVAILABLE and settings.google_vision_enabled and settings.google_vision_api_key:
            try:
                self.vision_client = vision.ImageAnnotatorClient()
                logger.info("Google Vision API client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Google Vision API: {e}")
    
    def extract_text(self, image_path: Union[str, Path], engine: str = "tesseract") -> Dict:
        """
        Extract text from image using specified OCR engine
        
        Args:
            image_path: Path to the image file
            engine: OCR engine to use ('tesseract', 'google_vision', 'hybrid')
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            image_path = Path(image_path)
            
            if not image_path.exists():
                raise FileNotFoundError(f"Image file not found: {image_path}")
            
            # Preprocess image
            processed_image = preprocessor.preprocess_image(image_path)
            
            # Extract text based on engine
            if engine == "tesseract":
                result = self._extract_with_tesseract(processed_image)
            elif engine == "google_vision" and self.vision_client:
                result = self._extract_with_google_vision(image_path)
            elif engine == "hybrid":
                result = self._extract_hybrid(processed_image, image_path)
            else:
                # Fallback to Tesseract
                result = self._extract_with_tesseract(processed_image)
            
            # Add metadata
            result.update({
                "image_path": str(image_path),
                "engine_used": engine,
                "preprocessing_applied": settings.enhance_image
            })
            
            logger.info(f"Text extraction completed for {image_path} using {engine}")
            return result
            
        except Exception as e:
            logger.error(f"Error extracting text from {image_path}: {str(e)}")
            raise
    
    def _extract_with_tesseract(self, image: np.ndarray) -> Dict:
        """Extract text using Tesseract OCR"""
        try:
            # Set Tesseract path if specified
            if settings.tesseract_path:
                pytesseract.pytesseract.tesseract_cmd = settings.tesseract_path
            
            # Extract text with confidence scores
            data = pytesseract.image_to_data(
                image, 
                config=self.tesseract_config,
                output_type=pytesseract.Output.DICT
            )
            
            # Extract raw text
            raw_text = pytesseract.image_to_string(image, config=self.tesseract_config)
            
            # Process results
            words = []
            lines = []
            current_line = []
            current_line_num = -1
            
            for i in range(len(data['text'])):
                confidence = int(data['conf'][i])
                text = data['text'][i].strip()
                
                if confidence > 0 and text:
                    word_info = {
                        'text': text,
                        'confidence': confidence,
                        'bbox': {
                            'x': data['left'][i],
                            'y': data['top'][i],
                            'width': data['width'][i],
                            'height': data['height'][i]
                        },
                        'line_num': data['line_num'][i],
                        'word_num': data['word_num'][i]
                    }
                    
                    words.append(word_info)
                    
                    # Group words into lines
                    if data['line_num'][i] != current_line_num:
                        if current_line:
                            lines.append({
                                'text': ' '.join([w['text'] for w in current_line]),
                                'confidence': np.mean([w['confidence'] for w in current_line]),
                                'words': current_line,
                                'line_num': current_line_num
                            })
                        current_line = [word_info]
                        current_line_num = data['line_num'][i]
                    else:
                        current_line.append(word_info)
            
            # Add last line
            if current_line:
                lines.append({
                    'text': ' '.join([w['text'] for w in current_line]),
                    'confidence': np.mean([w['confidence'] for w in current_line]),
                    'words': current_line,
                    'line_num': current_line_num
                })
            
            # Calculate overall confidence
            overall_confidence = np.mean([w['confidence'] for w in words]) if words else 0
            
            return {
                'raw_text': raw_text.strip(),
                'words': words,
                'lines': lines,
                'confidence': {
                    'overall': float(overall_confidence),
                    'word_count': len(words),
                    'high_confidence_words': len([w for w in words if w['confidence'] >= 80]),
                    'low_confidence_words': len([w for w in words if w['confidence'] < 60])
                },
                'engine': 'tesseract',
                'language': settings.supported_languages
            }
            
        except Exception as e:
            logger.error(f"Tesseract extraction failed: {str(e)}")
            raise
    
    def _extract_with_google_vision(self, image_path: Path) -> Dict:
        """Extract text using Google Vision API"""
        if not self.vision_client:
            raise ValueError("Google Vision API not available")
        
        try:
            # Read image file
            with open(image_path, 'rb') as image_file:
                content = image_file.read()
            
            image = vision.Image(content=content)
            
            # Perform text detection
            response = self.vision_client.text_detection(image=image)
            texts = response.text_annotations
            
            if response.error.message:
                raise Exception(f"Google Vision API error: {response.error.message}")
            
            if not texts:
                return {
                    'raw_text': '',
                    'words': [],
                    'lines': [],
                    'confidence': {'overall': 0},
                    'engine': 'google_vision'
                }
            
            # First annotation contains the full text
            raw_text = texts[0].description
            
            # Process individual text elements
            words = []
            for text in texts[1:]:  # Skip first element (full text)
                # Google Vision doesn't provide confidence scores directly
                # We'll estimate based on bounding box properties
                vertices = text.bounding_poly.vertices
                confidence = self._estimate_google_vision_confidence(text.description, vertices)
                
                word_info = {
                    'text': text.description,
                    'confidence': confidence,
                    'bbox': {
                        'x': min([v.x for v in vertices]),
                        'y': min([v.y for v in vertices]),
                        'width': max([v.x for v in vertices]) - min([v.x for v in vertices]),
                        'height': max([v.y for v in vertices]) - min([v.y for v in vertices])
                    }
                }
                words.append(word_info)
            
            # Group words into lines (simplified)
            lines = self._group_words_into_lines(words)
            
            # Calculate overall confidence
            overall_confidence = np.mean([w['confidence'] for w in words]) if words else 0
            
            return {
                'raw_text': raw_text,
                'words': words,
                'lines': lines,
                'confidence': {
                    'overall': float(overall_confidence),
                    'word_count': len(words)
                },
                'engine': 'google_vision'
            }
            
        except Exception as e:
            logger.error(f"Google Vision extraction failed: {str(e)}")
            raise
    
    def _extract_hybrid(self, processed_image: np.ndarray, image_path: Path) -> Dict:
        """Extract text using hybrid approach (Tesseract + Google Vision)"""
        results = []
        
        # Try Tesseract first
        try:
            tesseract_result = self._extract_with_tesseract(processed_image)
            tesseract_result['engine'] = 'tesseract'
            results.append(tesseract_result)
        except Exception as e:
            logger.warning(f"Tesseract failed in hybrid mode: {e}")
        
        # Try Google Vision if available
        if self.vision_client:
            try:
                vision_result = self._extract_with_google_vision(image_path)
                vision_result['engine'] = 'google_vision'
                results.append(vision_result)
            except Exception as e:
                logger.warning(f"Google Vision failed in hybrid mode: {e}")
        
        if not results:
            raise Exception("All OCR engines failed")
        
        # Combine results (prefer higher confidence)
        best_result = max(results, key=lambda x: x['confidence']['overall'])
        
        # Merge information from all engines
        combined_result = best_result.copy()
        combined_result['engine'] = 'hybrid'
        combined_result['engine_results'] = results
        
        return combined_result
    
    def _estimate_google_vision_confidence(self, text: str, vertices: List) -> float:
        """Estimate confidence for Google Vision results"""
        # Simple heuristic based on text characteristics
        confidence = 85.0  # Base confidence for Google Vision
        
        # Adjust based on text length
        if len(text) < 2:
            confidence -= 20
        elif len(text) > 10:
            confidence += 5
        
        # Adjust based on character types
        if text.isdigit():
            confidence += 10  # Numbers are usually well-recognized
        elif text.isalpha():
            confidence += 5   # Pure text is reliable
        elif any(c in text for c in '!@#$%^&*()'):
            confidence -= 15  # Special characters are less reliable
        
        # Adjust based on bounding box regularity
        if vertices and len(vertices) >= 4:
            widths = [abs(vertices[1].x - vertices[0].x), abs(vertices[2].x - vertices[3].x)]
            heights = [abs(vertices[3].y - vertices[0].y), abs(vertices[2].y - vertices[1].y)]
            
            if max(widths) - min(widths) < 5 and max(heights) - min(heights) < 5:
                confidence += 5  # Regular bounding box
        
        return max(0, min(100, confidence))
    
    def _group_words_into_lines(self, words: List[Dict]) -> List[Dict]:
        """Group words into lines based on y-coordinates"""
        if not words:
            return []
        
        # Sort words by y-coordinate
        sorted_words = sorted(words, key=lambda w: w['bbox']['y'])
        
        lines = []
        current_line = []
        current_y = sorted_words[0]['bbox']['y']
        line_height_threshold = 20  # Pixels
        
        for word in sorted_words:
            word_y = word['bbox']['y']
            
            if abs(word_y - current_y) <= line_height_threshold:
                current_line.append(word)
            else:
                if current_line:
                    # Sort current line by x-coordinate
                    current_line.sort(key=lambda w: w['bbox']['x'])
                    lines.append({
                        'text': ' '.join([w['text'] for w in current_line]),
                        'confidence': np.mean([w['confidence'] for w in current_line]),
                        'words': current_line
                    })
                
                current_line = [word]
                current_y = word_y
        
        # Add last line
        if current_line:
            current_line.sort(key=lambda w: w['bbox']['x'])
            lines.append({
                'text': ' '.join([w['text'] for w in current_line]),
                'confidence': np.mean([w['confidence'] for w in current_line]),
                'words': current_line
            })
        
        return lines
    
    def extract_text_from_pdf(self, pdf_path: Union[str, Path]) -> List[Dict]:
        """Extract text from PDF file"""
        try:
            from pdf2image import convert_from_path
            
            pdf_path = Path(pdf_path)
            images = convert_from_path(pdf_path, dpi=settings.image_dpi)
            
            results = []
            for i, image in enumerate(images):
                # Save temporary image
                temp_image_path = Path(settings.temp_dir) / f"page_{i+1}.png"
                image.save(temp_image_path)
                
                # Extract text
                page_result = self.extract_text(temp_image_path)
                page_result['page_number'] = i + 1
                results.append(page_result)
                
                # Clean up temporary file
                temp_image_path.unlink()
            
            return results
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF {pdf_path}: {str(e)}")
            raise


# Global text extractor instance
text_extractor = TextExtractor()


def extract_text(image_path: Union[str, Path], engine: str = "tesseract") -> Dict:
    """Convenience function for text extraction"""
    return text_extractor.extract_text(image_path, engine)
