/**
 * FinScanAI API Service
 * Centralized API communication layer for the frontend application
 */

import axios from 'axios';
import toast from 'react-hot-toast';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const OCR_SERVICE_URL = process.env.REACT_APP_OCR_SERVICE_URL || 'http://localhost:8000';

// Create axios instances
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

const ocrClient = axios.create({
  baseURL: OCR_SERVICE_URL,
  timeout: 300000, // 5 minutes for OCR processing
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});

// Request interceptors
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('finscannai-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptors
apiClient.interceptors.response.use(
  (response) => {
    // Check for new token in headers
    const newToken = response.headers['x-new-token'];
    if (newToken) {
      localStorage.setItem('finscannai-token', newToken);
    }
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Unauthorized - clear token and redirect to login
      localStorage.removeItem('finscannai-token');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      // Server error
      toast.error('Server error. Please try again later.');
    }
    return Promise.reject(error);
  }
);

// OCR Client interceptors
ocrClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please try again.');
    } else if (error.response?.status >= 500) {
      toast.error('OCR service error. Please try again later.');
    }
    return Promise.reject(error);
  }
);

// Authentication Service
export const authService = {
  // Register new user
  register: async (userData) => {
    try {
      const response = await apiClient.post('/auth/register', userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  },

  // Get user profile
  getProfile: async () => {
    try {
      const response = await apiClient.get('/auth/profile');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch profile');
    }
  },

  // Update user profile
  updateProfile: async (userData) => {
    try {
      const response = await apiClient.put('/auth/profile', userData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to update profile');
    }
  },

  // Change password
  changePassword: async (passwordData) => {
    try {
      const response = await apiClient.put('/auth/password', passwordData);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to change password');
    }
  },

  // Get user statistics
  getStats: async () => {
    try {
      const response = await apiClient.get('/auth/stats');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch statistics');
    }
  },

  // Logout
  logout: async () => {
    try {
      await apiClient.post('/auth/logout');
    } catch (error) {
      // Ignore logout errors
    } finally {
      localStorage.removeItem('finscannai-token');
    }
  },
};

// Document Service
export const documentService = {
  // Upload document
  uploadDocument: async (file, options = {}) => {
    try {
      const formData = new FormData();
      formData.append('document', file);
      
      // Add options as query parameters
      const params = new URLSearchParams(options);
      
      const response = await apiClient.post(`/upload?${params}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: options.onProgress,
      });
      
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Upload failed');
    }
  },

  // Get user documents
  getDocuments: async (page = 1, limit = 10) => {
    try {
      const response = await apiClient.get(`/upload/documents?page=${page}&limit=${limit}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch documents');
    }
  },

  // Get document details
  getDocument: async (documentId) => {
    try {
      const response = await apiClient.get(`/upload/documents/${documentId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to fetch document');
    }
  },

  // Delete document
  deleteDocument: async (documentId) => {
    try {
      const response = await apiClient.delete(`/upload/documents/${documentId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to delete document');
    }
  },

  // Download document
  downloadDocument: async (documentId) => {
    try {
      const response = await apiClient.get(`/upload/documents/${documentId}/download`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Failed to download document');
    }
  },
};

// OCR Service
export const ocrService = {
  // Process document with OCR
  processDocument: async (file, options = {}) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await ocrClient.post('/process', formData, {
        params: options,
        onUploadProgress: options.onProgress,
      });
      
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'OCR processing failed');
    }
  },

  // Get job status
  getJobStatus: async (jobId) => {
    try {
      const response = await ocrClient.get(`/jobs/${jobId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch job status');
    }
  },

  // List all jobs
  getJobs: async () => {
    try {
      const response = await ocrClient.get('/jobs');
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to fetch jobs');
    }
  },

  // Delete job
  deleteJob: async (jobId) => {
    try {
      const response = await ocrClient.delete(`/jobs/${jobId}`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.detail || 'Failed to delete job');
    }
  },

  // Health check
  healthCheck: async () => {
    try {
      const response = await ocrClient.get('/health');
      return response.data;
    } catch (error) {
      throw new Error('OCR service is unavailable');
    }
  },
};

// Export Service
export const exportService = {
  // Export to Excel
  exportToExcel: async (documentId) => {
    try {
      const response = await apiClient.get(`/export/${documentId}/excel`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'Excel export failed');
    }
  },

  // Export to CSV
  exportToCSV: async (documentId) => {
    try {
      const response = await apiClient.get(`/export/${documentId}/csv`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'CSV export failed');
    }
  },

  // Export to JSON
  exportToJSON: async (documentId) => {
    try {
      const response = await apiClient.get(`/export/${documentId}/json`);
      return response.data;
    } catch (error) {
      throw new Error(error.response?.data?.message || 'JSON export failed');
    }
  },
};

// Utility functions
export const apiUtils = {
  // Download blob as file
  downloadBlob: (blob, filename) => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  // Format file size
  formatFileSize: (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Validate file type
  validateFileType: (file, allowedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'tiff']) => {
    const extension = file.name.split('.').pop().toLowerCase();
    return allowedTypes.includes(extension);
  },

  // Validate file size
  validateFileSize: (file, maxSize = 10 * 1024 * 1024) => { // 10MB default
    return file.size <= maxSize;
  },
};

// Export default API client for custom requests
export default apiClient;
