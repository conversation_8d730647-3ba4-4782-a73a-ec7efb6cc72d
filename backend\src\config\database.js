/**
 * FinScanAI Database Configuration
 * Handles database connection setup for both SQLite (development) and PostgreSQL (production)
 */

const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config();

const environment = process.env.NODE_ENV || 'development';
const databaseUrl = process.env.DATABASE_URL;

let sequelize;

if (environment === 'production' && databaseUrl && databaseUrl.startsWith('postgresql://')) {
  // Production PostgreSQL configuration
  sequelize = new Sequelize(databaseUrl, {
    dialect: 'postgres',
    protocol: 'postgres',
    logging: process.env.LOG_LEVEL === 'debug' ? console.log : false,
    dialectOptions: {
      ssl: process.env.DATABASE_SSL === 'true' ? {
        require: true,
        rejectUnauthorized: false
      } : false
    },
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    retry: {
      match: [
        /ETIMEDOUT/,
        /EHOSTUNREACH/,
        /ECONNRESET/,
        /ECONNREFUSED/,
        /ETIMEDOUT/,
        /ESOCKETTIMEDOUT/,
        /EHOSTUNREACH/,
        /EPIPE/,
        /EAI_AGAIN/,
        /SequelizeConnectionError/,
        /SequelizeConnectionRefusedError/,
        /SequelizeHostNotFoundError/,
        /SequelizeHostNotReachableError/,
        /SequelizeInvalidConnectionError/,
        /SequelizeConnectionTimedOutError/
      ],
      max: 3
    }
  });
} else {
  // Development SQLite configuration
  const dbPath = databaseUrl && databaseUrl.startsWith('sqlite:') 
    ? databaseUrl.replace('sqlite:', '')
    : path.join(__dirname, '../../database/finscannai.db');

  sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: dbPath,
    logging: process.env.LOG_LEVEL === 'debug' ? console.log : false,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  });
}

/**
 * Test database connection
 * @returns {Promise<boolean>} Connection status
 */
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully.');
    return true;
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error.message);
    return false;
  }
};

/**
 * Initialize database with tables
 * @returns {Promise<void>}
 */
const initializeDatabase = async () => {
  try {
    // Import models
    const User = require('../models/User');
    const Document = require('../models/Document');
    const ProcessingResult = require('../models/ProcessingResult');

    // Define associations
    User.hasMany(Document, { foreignKey: 'userId', as: 'documents' });
    Document.belongsTo(User, { foreignKey: 'userId', as: 'user' });
    
    Document.hasOne(ProcessingResult, { foreignKey: 'documentId', as: 'result' });
    ProcessingResult.belongsTo(Document, { foreignKey: 'documentId', as: 'document' });

    // Sync database
    await sequelize.sync({ 
      force: process.env.DB_FORCE_SYNC === 'true',
      alter: process.env.DB_ALTER_SYNC === 'true'
    });
    
    console.log('✅ Database tables synchronized successfully.');
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    throw error;
  }
};

/**
 * Close database connection
 * @returns {Promise<void>}
 */
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log('✅ Database connection closed successfully.');
  } catch (error) {
    console.error('❌ Error closing database connection:', error.message);
  }
};

module.exports = {
  sequelize,
  testConnection,
  initializeDatabase,
  closeConnection,
  Sequelize
};
