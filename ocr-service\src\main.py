"""
FinScanAI OCR Service
FastAPI-based OCR microservice for financial document processing
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Dict, List, Optional
import uuid

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel
import uvicorn

from .config.settings import settings
from .processors.text_extractor import text_extractor
from .processors.image_preprocessor import preprocessor
from .algorithms.table_detection import table_detector
from .algorithms.financial_validator import financial_validator

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Advanced OCR service for financial document processing",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ProcessingRequest(BaseModel):
    language: str = "eng+vie"
    engine: str = "tesseract"
    enhance_image: bool = True
    detect_tables: bool = True
    extract_numbers: bool = True
    validate_financial: bool = True

class ProcessingResponse(BaseModel):
    job_id: str
    status: str
    message: str
    processing_time: Optional[float] = None
    result: Optional[Dict] = None

class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: str
    services: Dict[str, str]

# In-memory job storage (use Redis in production)
processing_jobs: Dict[str, Dict] = {}

@app.on_event("startup")
async def startup_event():
    """Initialize service on startup"""
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    
    # Create required directories
    settings.create_directories()
    
    # Test OCR engines
    try:
        # Test Tesseract
        import pytesseract
        pytesseract.get_tesseract_version()
        logger.info("✅ Tesseract OCR engine available")
    except Exception as e:
        logger.warning(f"⚠️ Tesseract not available: {e}")
    
    # Test Google Vision API
    if settings.google_vision_enabled:
        try:
            from google.cloud import vision
            client = vision.ImageAnnotatorClient()
            logger.info("✅ Google Vision API available")
        except Exception as e:
            logger.warning(f"⚠️ Google Vision API not available: {e}")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        version=settings.app_version,
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
        services={
            "tesseract": "available",
            "google_vision": "available" if settings.google_vision_enabled else "disabled",
            "table_detection": "available",
            "financial_validation": "available"
        }
    )

@app.post("/process", response_model=ProcessingResponse)
async def process_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    language: str = "eng+vie",
    engine: str = "tesseract",
    enhance_image: bool = True,
    detect_tables: bool = True,
    extract_numbers: bool = True,
    validate_financial: bool = True
):
    """Process uploaded document with OCR"""
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    file_extension = Path(file.filename).suffix.lower().lstrip('.')
    if file_extension not in settings.allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Allowed: {', '.join(settings.allowed_extensions)}"
        )
    
    # Check file size
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size: {settings.max_file_size / 1024 / 1024:.1f}MB"
        )
    
    # Generate job ID
    job_id = str(uuid.uuid4())
    
    # Save uploaded file
    upload_path = Path(settings.upload_dir) / f"{job_id}_{file.filename}"
    
    try:
        with open(upload_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Initialize job
        processing_jobs[job_id] = {
            "status": "queued",
            "file_path": str(upload_path),
            "original_filename": file.filename,
            "created_at": time.time(),
            "parameters": {
                "language": language,
                "engine": engine,
                "enhance_image": enhance_image,
                "detect_tables": detect_tables,
                "extract_numbers": extract_numbers,
                "validate_financial": validate_financial
            }
        }
        
        # Start background processing
        background_tasks.add_task(
            process_document_background,
            job_id,
            str(upload_path),
            {
                "language": language,
                "engine": engine,
                "enhance_image": enhance_image,
                "detect_tables": detect_tables,
                "extract_numbers": extract_numbers,
                "validate_financial": validate_financial
            }
        )
        
        return ProcessingResponse(
            job_id=job_id,
            status="queued",
            message="Document queued for processing"
        )
        
    except Exception as e:
        logger.error(f"Error processing upload: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process upload")

async def process_document_background(job_id: str, file_path: str, parameters: Dict):
    """Background task for document processing"""
    start_time = time.time()
    
    try:
        # Update job status
        processing_jobs[job_id]["status"] = "processing"
        processing_jobs[job_id]["started_at"] = start_time
        
        # Extract text
        logger.info(f"Starting OCR processing for job {job_id}")
        
        if file_path.lower().endswith('.pdf'):
            # Handle PDF files
            text_results = text_extractor.extract_text_from_pdf(file_path)
        else:
            # Handle image files
            text_result = text_extractor.extract_text(file_path, parameters["engine"])
            text_results = [text_result]
        
        # Process each page/result
        processed_results = []
        for i, result in enumerate(text_results):
            page_result = {
                "page": i + 1,
                "text_extraction": result,
                "tables": [],
                "financial_data": {},
                "validation": {}
            }
            
            # Table detection
            if parameters["detect_tables"]:
                try:
                    # Load image for table detection
                    if file_path.lower().endswith('.pdf'):
                        # For PDF, we'd need to convert page to image
                        # Simplified for now
                        page_result["tables"] = []
                    else:
                        tables = table_detector.detect_tables(file_path)
                        page_result["tables"] = tables
                except Exception as e:
                    logger.warning(f"Table detection failed: {e}")
                    page_result["tables"] = []
            
            # Financial data extraction
            if parameters["extract_numbers"]:
                try:
                    financial_data = financial_validator.extract_financial_data(result["raw_text"])
                    page_result["financial_data"] = financial_data
                except Exception as e:
                    logger.warning(f"Financial data extraction failed: {e}")
                    page_result["financial_data"] = {}
            
            # Financial validation
            if parameters["validate_financial"]:
                try:
                    validation = financial_validator.validate_document(result["raw_text"])
                    page_result["validation"] = validation
                except Exception as e:
                    logger.warning(f"Financial validation failed: {e}")
                    page_result["validation"] = {}
            
            processed_results.append(page_result)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Update job with results
        processing_jobs[job_id].update({
            "status": "completed",
            "completed_at": time.time(),
            "processing_time": processing_time,
            "result": {
                "pages": processed_results,
                "summary": {
                    "total_pages": len(processed_results),
                    "total_text_length": sum(len(r["text_extraction"]["raw_text"]) for r in processed_results),
                    "average_confidence": sum(r["text_extraction"]["confidence"]["overall"] for r in processed_results) / len(processed_results) if processed_results else 0,
                    "tables_detected": sum(len(r["tables"]) for r in processed_results),
                    "processing_time": processing_time
                }
            }
        })
        
        logger.info(f"Completed processing job {job_id} in {processing_time:.2f}s")
        
    except Exception as e:
        logger.error(f"Error processing job {job_id}: {str(e)}")
        processing_jobs[job_id].update({
            "status": "failed",
            "error": str(e),
            "completed_at": time.time()
        })

@app.get("/jobs/{job_id}", response_model=ProcessingResponse)
async def get_job_status(job_id: str):
    """Get processing job status and results"""
    if job_id not in processing_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = processing_jobs[job_id]
    
    return ProcessingResponse(
        job_id=job_id,
        status=job["status"],
        message=f"Job is {job['status']}",
        processing_time=job.get("processing_time"),
        result=job.get("result")
    )

@app.get("/jobs")
async def list_jobs():
    """List all processing jobs"""
    return {
        "jobs": [
            {
                "job_id": job_id,
                "status": job["status"],
                "created_at": job["created_at"],
                "original_filename": job["original_filename"]
            }
            for job_id, job in processing_jobs.items()
        ]
    }

@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    """Delete processing job and associated files"""
    if job_id not in processing_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = processing_jobs[job_id]
    
    # Delete file if exists
    file_path = Path(job["file_path"])
    if file_path.exists():
        file_path.unlink()
    
    # Remove job from memory
    del processing_jobs[job_id]
    
    return {"message": "Job deleted successfully"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        workers=settings.workers if not settings.debug else 1
    )
