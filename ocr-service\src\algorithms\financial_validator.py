"""
FinScanAI Financial Data Validator
Custom algorithms for extracting and validating financial data from OCR text
"""

import re
import logging
from typing import Dict, List, Optional, Tuple
from decimal import Decimal, InvalidOperation
import dateparser
from datetime import datetime

logger = logging.getLogger(__name__)


class FinancialValidator:
    """Custom financial data extraction and validation"""
    
    def __init__(self):
        # Currency patterns
        self.currency_patterns = {
            'USD': [r'\$\s*[\d,]+\.?\d*', r'USD\s*[\d,]+\.?\d*', r'[\d,]+\.?\d*\s*USD'],
            'VND': [r'[\d,]+\.?\d*\s*VND', r'[\d,]+\.?\d*\s*₫', r'₫\s*[\d,]+\.?\d*'],
            'EUR': [r'€\s*[\d,]+\.?\d*', r'EUR\s*[\d,]+\.?\d*', r'[\d,]+\.?\d*\s*EUR'],
            'GBP': [r'£\s*[\d,]+\.?\d*', r'GBP\s*[\d,]+\.?\d*', r'[\d,]+\.?\d*\s*GBP']
        }
        
        # Number patterns
        self.number_patterns = [
            r'\b\d{1,3}(?:,\d{3})*(?:\.\d{2})?\b',  # Standard format: 1,234.56
            r'\b\d+\.\d{2}\b',                       # Simple decimal: 123.45
            r'\b\d+,\d{3}\b',                        # Thousands: 1,234
            r'\b\d{1,3}(?:\.\d{3})*(?:,\d{2})?\b',  # European format: 1.234,56
        ]
        
        # Date patterns
        self.date_patterns = [
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',   # MM/DD/YYYY or DD/MM/YYYY
            r'\b\d{2,4}[/-]\d{1,2}[/-]\d{1,2}\b',   # YYYY/MM/DD
            r'\b\d{1,2}\s+\w+\s+\d{2,4}\b',         # DD Month YYYY
            r'\b\w+\s+\d{1,2},?\s+\d{2,4}\b'        # Month DD, YYYY
        ]
        
        # Financial keywords
        self.financial_keywords = {
            'revenue': ['revenue', 'sales', 'income', 'turnover', 'doanh thu'],
            'expense': ['expense', 'cost', 'expenditure', 'chi phí', 'chi tiêu'],
            'profit': ['profit', 'earnings', 'lợi nhuận', 'thu nhập'],
            'loss': ['loss', 'deficit', 'lỗ', 'thâm hụt'],
            'asset': ['asset', 'tài sản'],
            'liability': ['liability', 'debt', 'nợ', 'công nợ'],
            'equity': ['equity', 'vốn chủ sở hữu'],
            'cash': ['cash', 'tiền mặt', 'tiền'],
            'tax': ['tax', 'thuế'],
            'total': ['total', 'sum', 'tổng', 'cộng']
        }
    
    def extract_financial_data(self, text: str) -> Dict:
        """Extract financial data from OCR text"""
        try:
            # Clean and normalize text
            cleaned_text = self._clean_text(text)
            
            # Extract different types of financial data
            numbers = self._extract_numbers(cleaned_text)
            currencies = self._extract_currencies(cleaned_text)
            dates = self._extract_dates(cleaned_text)
            entities = self._extract_financial_entities(cleaned_text)
            
            # Analyze financial context
            context = self._analyze_financial_context(cleaned_text)
            
            return {
                'numbers': numbers,
                'currencies': currencies,
                'dates': dates,
                'entities': entities,
                'context': context,
                'summary': {
                    'total_numbers': len(numbers),
                    'total_currencies': len(currencies),
                    'total_dates': len(dates),
                    'document_type': self._classify_document_type(cleaned_text)
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting financial data: {str(e)}")
            return {}
    
    def validate_document(self, text: str) -> Dict:
        """Validate financial document for consistency and accuracy"""
        try:
            validation_results = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'confidence': 0.0
            }
            
            # Extract financial data
            financial_data = self.extract_financial_data(text)
            
            # Validate number formats
            number_validation = self._validate_number_formats(financial_data.get('numbers', []))
            validation_results['errors'].extend(number_validation['errors'])
            validation_results['warnings'].extend(number_validation['warnings'])
            
            # Validate currency consistency
            currency_validation = self._validate_currency_consistency(financial_data.get('currencies', []))
            validation_results['errors'].extend(currency_validation['errors'])
            validation_results['warnings'].extend(currency_validation['warnings'])
            
            # Validate date consistency
            date_validation = self._validate_date_consistency(financial_data.get('dates', []))
            validation_results['errors'].extend(date_validation['errors'])
            validation_results['warnings'].extend(date_validation['warnings'])
            
            # Validate financial logic
            logic_validation = self._validate_financial_logic(financial_data)
            validation_results['errors'].extend(logic_validation['errors'])
            validation_results['warnings'].extend(logic_validation['warnings'])
            
            # Calculate overall confidence
            validation_results['confidence'] = self._calculate_validation_confidence(validation_results)
            validation_results['is_valid'] = len(validation_results['errors']) == 0
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating document: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Validation failed: {str(e)}"],
                'warnings': [],
                'confidence': 0.0
            }
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for processing"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Normalize currency symbols
        text = text.replace('₫', 'VND')
        text = text.replace('$', 'USD')
        text = text.replace('€', 'EUR')
        text = text.replace('£', 'GBP')
        
        return text.strip()
    
    def _extract_numbers(self, text: str) -> List[Dict]:
        """Extract numerical values from text"""
        numbers = []
        
        for pattern in self.number_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                raw_value = match.group()
                
                # Parse number
                parsed_value = self._parse_number(raw_value)
                if parsed_value is not None:
                    numbers.append({
                        'raw': raw_value,
                        'value': float(parsed_value),
                        'position': match.span(),
                        'context': self._get_context(text, match.span(), 50)
                    })
        
        # Remove duplicates and sort by position
        numbers = self._deduplicate_numbers(numbers)
        numbers.sort(key=lambda x: x['position'][0])
        
        return numbers
    
    def _extract_currencies(self, text: str) -> List[Dict]:
        """Extract currency values from text"""
        currencies = []
        
        for currency, patterns in self.currency_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    raw_value = match.group()
                    
                    # Extract numeric part
                    numeric_part = re.search(r'[\d,]+\.?\d*', raw_value)
                    if numeric_part:
                        parsed_value = self._parse_number(numeric_part.group())
                        if parsed_value is not None:
                            currencies.append({
                                'raw': raw_value,
                                'currency': currency,
                                'value': float(parsed_value),
                                'position': match.span(),
                                'context': self._get_context(text, match.span(), 50)
                            })
        
        return currencies
    
    def _extract_dates(self, text: str) -> List[Dict]:
        """Extract dates from text"""
        dates = []
        
        for pattern in self.date_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                raw_date = match.group()
                
                # Parse date
                parsed_date = dateparser.parse(raw_date)
                if parsed_date:
                    dates.append({
                        'raw': raw_date,
                        'parsed': parsed_date.isoformat(),
                        'position': match.span(),
                        'context': self._get_context(text, match.span(), 30)
                    })
        
        return dates
    
    def _extract_financial_entities(self, text: str) -> List[Dict]:
        """Extract financial entities and their associated values"""
        entities = []
        
        for entity_type, keywords in self.financial_keywords.items():
            for keyword in keywords:
                pattern = rf'\b{re.escape(keyword)}\b.*?[\d,]+\.?\d*'
                matches = re.finditer(pattern, text, re.IGNORECASE)
                
                for match in matches:
                    # Extract associated number
                    number_match = re.search(r'[\d,]+\.?\d*', match.group())
                    if number_match:
                        parsed_value = self._parse_number(number_match.group())
                        if parsed_value is not None:
                            entities.append({
                                'type': entity_type,
                                'keyword': keyword,
                                'raw': match.group(),
                                'value': float(parsed_value),
                                'position': match.span(),
                                'context': self._get_context(text, match.span(), 100)
                            })
        
        return entities
    
    def _parse_number(self, number_str: str) -> Optional[Decimal]:
        """Parse number string to Decimal"""
        try:
            # Remove spaces
            number_str = number_str.replace(' ', '')
            
            # Handle different formats
            if ',' in number_str and '.' in number_str:
                # Determine format based on position
                comma_pos = number_str.rfind(',')
                dot_pos = number_str.rfind('.')
                
                if dot_pos > comma_pos:
                    # Format: 1,234.56
                    number_str = number_str.replace(',', '')
                else:
                    # Format: 1.234,56
                    number_str = number_str.replace('.', '').replace(',', '.')
            elif ',' in number_str:
                # Could be thousands separator or decimal
                if len(number_str.split(',')[-1]) == 2:
                    # Decimal comma: 1234,56
                    number_str = number_str.replace(',', '.')
                else:
                    # Thousands separator: 1,234
                    number_str = number_str.replace(',', '')
            
            return Decimal(number_str)
            
        except (InvalidOperation, ValueError):
            return None
    
    def _get_context(self, text: str, position: Tuple[int, int], window: int) -> str:
        """Get context around a position in text"""
        start = max(0, position[0] - window)
        end = min(len(text), position[1] + window)
        return text[start:end].strip()
    
    def _deduplicate_numbers(self, numbers: List[Dict]) -> List[Dict]:
        """Remove duplicate numbers based on position overlap"""
        if not numbers:
            return []
        
        # Sort by position
        numbers.sort(key=lambda x: x['position'][0])
        
        deduplicated = [numbers[0]]
        
        for current in numbers[1:]:
            last = deduplicated[-1]
            
            # Check for overlap
            if current['position'][0] < last['position'][1]:
                # Overlapping - keep the one with better context or higher value
                if len(current['context']) > len(last['context']):
                    deduplicated[-1] = current
            else:
                deduplicated.append(current)
        
        return deduplicated
    
    def _analyze_financial_context(self, text: str) -> Dict:
        """Analyze the financial context of the document"""
        context = {
            'document_language': 'en',  # Default
            'financial_period': None,
            'company_indicators': [],
            'financial_statements': []
        }
        
        # Detect language
        vietnamese_indicators = ['doanh thu', 'chi phí', 'lợi nhuận', 'tài sản', 'nợ']
        if any(indicator in text.lower() for indicator in vietnamese_indicators):
            context['document_language'] = 'vi'
        
        # Detect financial statement types
        statement_indicators = {
            'income_statement': ['income statement', 'profit and loss', 'bảng kết quả kinh doanh'],
            'balance_sheet': ['balance sheet', 'bảng cân đối kế toán'],
            'cash_flow': ['cash flow', 'lưu chuyển tiền tệ']
        }
        
        for statement_type, indicators in statement_indicators.items():
            if any(indicator in text.lower() for indicator in indicators):
                context['financial_statements'].append(statement_type)
        
        return context
    
    def _classify_document_type(self, text: str) -> str:
        """Classify the type of financial document"""
        text_lower = text.lower()
        
        if any(word in text_lower for word in ['invoice', 'bill', 'hóa đơn']):
            return 'invoice'
        elif any(word in text_lower for word in ['receipt', 'biên lai']):
            return 'receipt'
        elif any(word in text_lower for word in ['balance sheet', 'bảng cân đối']):
            return 'balance_sheet'
        elif any(word in text_lower for word in ['income statement', 'profit', 'bảng kết quả']):
            return 'income_statement'
        elif any(word in text_lower for word in ['cash flow', 'lưu chuyển tiền']):
            return 'cash_flow_statement'
        elif any(word in text_lower for word in ['bank statement', 'sao kê']):
            return 'bank_statement'
        else:
            return 'other'
    
    def _validate_number_formats(self, numbers: List[Dict]) -> Dict:
        """Validate number formats for consistency"""
        errors = []
        warnings = []
        
        if not numbers:
            warnings.append("No numbers detected in document")
            return {'errors': errors, 'warnings': warnings}
        
        # Check for inconsistent number formats
        formats = set()
        for number in numbers:
            raw = number['raw']
            if ',' in raw and '.' in raw:
                formats.add('mixed')
            elif ',' in raw:
                formats.add('comma')
            elif '.' in raw:
                formats.add('dot')
            else:
                formats.add('integer')
        
        if len(formats) > 2:
            warnings.append("Inconsistent number formats detected")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_currency_consistency(self, currencies: List[Dict]) -> Dict:
        """Validate currency consistency"""
        errors = []
        warnings = []
        
        if not currencies:
            warnings.append("No currency values detected")
            return {'errors': errors, 'warnings': warnings}
        
        # Check for multiple currencies
        currency_types = set(c['currency'] for c in currencies)
        if len(currency_types) > 1:
            warnings.append(f"Multiple currencies detected: {', '.join(currency_types)}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_date_consistency(self, dates: List[Dict]) -> Dict:
        """Validate date consistency"""
        errors = []
        warnings = []
        
        if not dates:
            warnings.append("No dates detected in document")
            return {'errors': errors, 'warnings': warnings}
        
        # Check for future dates
        current_date = datetime.now()
        for date_info in dates:
            parsed_date = datetime.fromisoformat(date_info['parsed'].replace('Z', '+00:00'))
            if parsed_date > current_date:
                warnings.append(f"Future date detected: {date_info['raw']}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_financial_logic(self, financial_data: Dict) -> Dict:
        """Validate financial logic and relationships"""
        errors = []
        warnings = []
        
        entities = financial_data.get('entities', [])
        
        # Check for basic financial relationships
        revenue_entities = [e for e in entities if e['type'] == 'revenue']
        expense_entities = [e for e in entities if e['type'] == 'expense']
        profit_entities = [e for e in entities if e['type'] == 'profit']
        
        if revenue_entities and expense_entities and profit_entities:
            total_revenue = sum(e['value'] for e in revenue_entities)
            total_expenses = sum(e['value'] for e in expense_entities)
            total_profit = sum(e['value'] for e in profit_entities)
            
            expected_profit = total_revenue - total_expenses
            if abs(expected_profit - total_profit) > total_revenue * 0.1:  # 10% tolerance
                warnings.append("Profit calculation may be inconsistent with revenue and expenses")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _calculate_validation_confidence(self, validation_results: Dict) -> float:
        """Calculate overall validation confidence"""
        base_confidence = 1.0
        
        # Reduce confidence for each error
        base_confidence -= len(validation_results['errors']) * 0.2
        
        # Reduce confidence for each warning
        base_confidence -= len(validation_results['warnings']) * 0.1
        
        return max(0.0, min(1.0, base_confidence))


# Global financial validator instance
financial_validator = FinancialValidator()


def extract_financial_data(text: str) -> Dict:
    """Convenience function for financial data extraction"""
    return financial_validator.extract_financial_data(text)


def validate_document(text: str) -> Dict:
    """Convenience function for document validation"""
    return financial_validator.validate_document(text)
