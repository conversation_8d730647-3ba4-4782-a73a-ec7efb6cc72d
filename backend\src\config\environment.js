/**
 * FinScanAI Environment Configuration
 * Centralized configuration management for all environment variables
 */

require('dotenv').config();

const config = {
  // Application Configuration
  app: {
    name: process.env.APP_NAME || 'FinScanAI',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.BACKEND_PORT || process.env.PORT || '5000', 10),
    host: process.env.BACKEND_HOST || 'localhost'
  },

  // Database Configuration
  database: {
    url: process.env.DATABASE_URL || 'sqlite:./database/finscannai.db',
    type: process.env.DATABASE_TYPE || 'sqlite',
    ssl: process.env.DATABASE_SSL === 'true',
    forcSync: process.env.DB_FORCE_SYNC === 'true',
    alterSync: process.env.DB_ALTER_SYNC === 'true'
  },

  // Authentication Configuration
  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10)
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760', 10), // 10MB
    allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'pdf,jpg,jpeg,png,tiff').split(','),
    uploadDir: process.env.UPLOAD_DIR || './uploads',
    tempDir: process.env.TEMP_DIR || './temp'
  },

  // OCR Service Configuration
  ocr: {
    serviceUrl: process.env.OCR_SERVICE_URL || 'http://localhost:8000',
    timeout: parseInt(process.env.PROCESSING_TIMEOUT || '300000', 10), // 5 minutes
    maxConcurrentJobs: parseInt(process.env.MAX_CONCURRENT_JOBS || '10', 10),
    confidenceThreshold: parseInt(process.env.OCR_CONFIDENCE_THRESHOLD || '60', 10)
  },

  // External APIs
  apis: {
    googleVision: {
      apiKey: process.env.GOOGLE_VISION_API_KEY,
      enabled: process.env.GOOGLE_VISION_ENABLED === 'true'
    }
  },

  // Security Configuration
  security: {
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '15', 10), // minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    helmetEnabled: process.env.HELMET_ENABLED !== 'false'
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'combined',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '10m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10)
  },

  // Redis Configuration (Optional)
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || '',
    url: process.env.REDIS_URL
  },

  // Email Configuration (Optional)
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587', 10),
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // Cloud Storage Configuration (Optional)
  storage: {
    provider: process.env.CLOUD_STORAGE_PROVIDER || 'local',
    aws: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET
    }
  },

  // Development Tools
  development: {
    debug: process.env.DEBUG || 'finscannai:*',
    swaggerEnabled: process.env.ENABLE_SWAGGER === 'true',
    corsDebug: process.env.ENABLE_CORS_DEBUG === 'true'
  },

  // Monitoring Configuration
  monitoring: {
    metricsEnabled: process.env.ENABLE_METRICS === 'true',
    metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
    healthCheckEndpoint: process.env.HEALTH_CHECK_ENDPOINT || '/health'
  }
};

/**
 * Validate required environment variables
 * @throws {Error} If required variables are missing
 */
const validateConfig = () => {
  const required = [
    'JWT_SECRET'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate file size
  if (config.upload.maxFileSize > 50 * 1024 * 1024) { // 50MB
    console.warn('⚠️  Warning: MAX_FILE_SIZE is set to more than 50MB');
  }

  // Validate JWT secret in production
  if (config.app.environment === 'production' && config.auth.jwtSecret.includes('change-this')) {
    throw new Error('JWT_SECRET must be changed in production environment');
  }
};

// Validate configuration on module load
if (process.env.NODE_ENV !== 'test') {
  validateConfig();
}

module.exports = config;
