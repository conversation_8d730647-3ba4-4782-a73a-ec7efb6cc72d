{"name": "finscannai-frontend", "version": "1.0.0", "description": "FinScanAI Frontend - React.js application for Financial Report OCR", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "axios": "^1.6.2", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.16", "recharts": "^2.8.0", "date-fns": "^2.30.0", "file-saver": "^2.0.5", "xlsx": "^0.18.5", "jspdf": "^2.5.1", "html2canvas": "^1.4.1"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/file-saver": "^2.0.7", "typescript": "^4.9.5", "eslint": "^8.55.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.7"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"no-console": "warn", "no-unused-vars": "error", "prefer-const": "error"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000", "homepage": ".", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}