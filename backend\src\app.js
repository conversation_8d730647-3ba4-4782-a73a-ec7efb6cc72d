/**
 * FinScanAI Backend Application
 * Main Express.js server for Financial Report OCR API
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
const fs = require('fs');

// Import configuration
const config = require('./config/environment');
const { testConnection, initializeDatabase } = require('./config/database');

// Import middleware
const authMiddleware = require('./middleware/auth');
const uploadMiddleware = require('./middleware/upload');
const validationMiddleware = require('./middleware/validation');

// Import routes
const authRoutes = require('./routes/auth');
const uploadRoutes = require('./routes/upload');
const exportRoutes = require('./routes/export');

// Create Express application
const app = express();

/**
 * Security Middleware
 */
if (config.security.helmetEnabled) {
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }));
}

/**
 * CORS Configuration
 */
app.use(cors({
  origin: config.security.corsOrigin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

/**
 * Rate Limiting
 */
const limiter = rateLimit({
  windowMs: config.security.rateLimitWindow * 60 * 1000, // Convert minutes to milliseconds
  max: config.security.rateLimitMaxRequests,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: config.security.rateLimitWindow
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

/**
 * General Middleware
 */
app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

/**
 * Logging Middleware
 */
if (config.app.environment !== 'test') {
  app.use(morgan(config.logging.format));
}

/**
 * Create required directories
 */
const createDirectories = () => {
  const dirs = [
    config.upload.uploadDir,
    config.upload.tempDir,
    path.dirname(config.logging.file)
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    }
  });
};

/**
 * Health Check Endpoint
 */
app.get('/health', async (req, res) => {
  try {
    const dbStatus = await testConnection();
    
    res.status(200).json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: config.app.version,
      environment: config.app.environment,
      services: {
        database: dbStatus ? 'connected' : 'disconnected',
        ocr: 'checking...' // TODO: Add OCR service health check
      },
      uptime: process.uptime()
    });
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

/**
 * API Routes
 */
app.use('/api/auth', authRoutes);
app.use('/api/upload', authMiddleware.authenticate, uploadRoutes);
app.use('/api/export', authMiddleware.authenticate, exportRoutes);

/**
 * Static file serving for uploads (with authentication)
 */
app.use('/api/files', authMiddleware.authenticate, express.static(config.upload.uploadDir));

/**
 * API Documentation (Development only)
 */
if (config.development.swaggerEnabled && config.app.environment === 'development') {
  // TODO: Add Swagger documentation
  app.get('/api/docs', (req, res) => {
    res.json({
      message: 'API Documentation',
      endpoints: {
        auth: {
          'POST /api/auth/register': 'Register new user',
          'POST /api/auth/login': 'User login',
          'POST /api/auth/logout': 'User logout',
          'GET /api/auth/profile': 'Get user profile'
        },
        upload: {
          'POST /api/upload': 'Upload document for processing',
          'GET /api/upload/documents': 'Get user documents',
          'GET /api/upload/documents/:id': 'Get document details',
          'DELETE /api/upload/documents/:id': 'Delete document'
        },
        export: {
          'GET /api/export/:id/excel': 'Export to Excel',
          'GET /api/export/:id/csv': 'Export to CSV',
          'GET /api/export/:id/json': 'Export to JSON'
        }
      }
    });
  });
}

/**
 * Error Handling Middleware
 */

// 404 Handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    message: `Cannot ${req.method} ${req.originalUrl}`,
    timestamp: new Date().toISOString()
  });
});

// Global Error Handler
app.use((error, req, res, next) => {
  console.error('❌ Error:', error);
  
  // Validation errors
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Validation Error',
      message: error.message,
      details: error.errors
    });
  }
  
  // JWT errors
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      error: 'Authentication Error',
      message: 'Invalid token'
    });
  }
  
  // Multer errors (file upload)
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(413).json({
      error: 'File Too Large',
      message: 'File size exceeds the maximum allowed limit'
    });
  }
  
  // Database errors
  if (error.name === 'SequelizeValidationError') {
    return res.status(400).json({
      error: 'Database Validation Error',
      message: error.message,
      details: error.errors.map(e => ({ field: e.path, message: e.message }))
    });
  }
  
  // Default error
  const statusCode = error.statusCode || error.status || 500;
  const message = config.app.environment === 'production' 
    ? 'Internal Server Error' 
    : error.message;
  
  res.status(statusCode).json({
    error: 'Server Error',
    message,
    ...(config.app.environment !== 'production' && { stack: error.stack })
  });
});

/**
 * Server Initialization
 */
const startServer = async () => {
  try {
    // Create required directories
    createDirectories();
    
    // Test database connection
    console.log('🔍 Testing database connection...');
    const dbConnected = await testConnection();
    
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }
    
    // Initialize database
    console.log('🗄️  Initializing database...');
    await initializeDatabase();
    
    // Start server
    const server = app.listen(config.app.port, config.app.host, () => {
      console.log(`
🚀 FinScanAI Backend Server Started
📍 Environment: ${config.app.environment}
🌐 Server: http://${config.app.host}:${config.app.port}
📊 Health Check: http://${config.app.host}:${config.app.port}/health
📚 API Docs: http://${config.app.host}:${config.app.port}/api/docs
      `);
    });
    
    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('🛑 SIGTERM received, shutting down gracefully...');
      server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
      });
    });
    
    process.on('SIGINT', () => {
      console.log('🛑 SIGINT received, shutting down gracefully...');
      server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
};

// Start server if this file is run directly
if (require.main === module) {
  startServer();
}

module.exports = app;
