# FinScanAI - Git Ignore Configuration

# ===========================================
# ENVIRONMENT & SECRETS
# ===========================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.env
config/secrets.json

# ===========================================
# NODE.JS & NPM
# ===========================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.yarn-integrity
.pnp
.pnp.js

# ===========================================
# PYTHON
# ===========================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/
env/
ENV/
.venv/
.ENV/
pip-log.txt
pip-delete-this-directory.txt

# ===========================================
# DATABASES
# ===========================================
*.db
*.sqlite
*.sqlite3
database/
db.sqlite3
*.sql.backup

# ===========================================
# UPLOADS & TEMPORARY FILES
# ===========================================
uploads/
temp/
tmp/
*.tmp
*.temp
processed/
cache/

# ===========================================
# LOGS
# ===========================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ===========================================
# DOCKER
# ===========================================
.dockerignore
docker-compose.override.yml
.docker/

# ===========================================
# IDE & EDITORS
# ===========================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# ===========================================
# TESTING
# ===========================================
coverage/
.nyc_output
.coverage
htmlcov/
.tox/
.pytest_cache/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# ===========================================
# BUILD OUTPUTS
# ===========================================
build/
dist/
out/
.next/
.nuxt/
.vuepress/dist

# ===========================================
# RUNTIME
# ===========================================
pids
*.pid
*.seed
*.pid.lock
.grunt
.lock-wscript
.wafpickle-N

# ===========================================
# CERTIFICATES & KEYS
# ===========================================
*.pem
*.key
*.crt
*.csr
ssl/
certs/

# ===========================================
# BACKUP FILES
# ===========================================
*.backup
*.bak
*.old
*.orig

# ===========================================
# PACKAGE MANAGERS
# ===========================================
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===========================================
# MISC
# ===========================================
.sass-cache/
.connect.lock
.typings/
.eslintcache
.parcel-cache
.cache/
.temp/
