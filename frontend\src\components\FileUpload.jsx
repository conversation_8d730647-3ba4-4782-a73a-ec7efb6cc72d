/**
 * FinScanAI File Upload Component
 * Advanced drag-and-drop file upload with progress tracking and validation
 */

import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CloudUploadIcon, 
  DocumentIcon, 
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { apiUtils } from '../services/api';
import toast from 'react-hot-toast';

const FileUpload = ({ 
  onFileSelect, 
  onUploadComplete, 
  maxFiles = 5, 
  maxSize = 10 * 1024 * 1024, // 10MB
  allowedTypes = ['pdf', 'jpg', 'jpeg', 'png', 'tiff'],
  className = ''
}) => {
  const [files, setFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});

  const onDrop = useCallback((acceptedFiles, rejectedFiles) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach(error => {
        if (error.code === 'file-too-large') {
          toast.error(`File "${file.name}" is too large. Maximum size is ${apiUtils.formatFileSize(maxSize)}`);
        } else if (error.code === 'file-invalid-type') {
          toast.error(`File "${file.name}" has an invalid type. Allowed types: ${allowedTypes.join(', ')}`);
        } else if (error.code === 'too-many-files') {
          toast.error(`Too many files. Maximum allowed: ${maxFiles}`);
        } else {
          toast.error(`Error with file "${file.name}": ${error.message}`);
        }
      });
    });

    // Handle accepted files
    const newFiles = acceptedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'pending', // pending, uploading, completed, error
      progress: 0,
      preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : null
    }));

    setFiles(prev => [...prev, ...newFiles]);
    
    // Notify parent component
    if (onFileSelect) {
      onFileSelect(newFiles);
    }
  }, [maxFiles, maxSize, allowedTypes, onFileSelect]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    maxFiles: maxFiles - files.length,
    maxSize,
    accept: {
      'application/pdf': ['.pdf'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/png': ['.png'],
      'image/tiff': ['.tiff', '.tif']
    },
    disabled: uploading || files.length >= maxFiles
  });

  const removeFile = (fileId) => {
    setFiles(prev => {
      const updatedFiles = prev.filter(f => f.id !== fileId);
      const removedFile = prev.find(f => f.id === fileId);
      
      // Revoke object URL to prevent memory leaks
      if (removedFile?.preview) {
        URL.revokeObjectURL(removedFile.preview);
      }
      
      return updatedFiles;
    });
    
    // Remove from upload progress
    setUploadProgress(prev => {
      const updated = { ...prev };
      delete updated[fileId];
      return updated;
    });
  };

  const clearAllFiles = () => {
    // Revoke all object URLs
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    
    setFiles([]);
    setUploadProgress({});
  };

  const getFileIcon = (file) => {
    if (file.type === 'application/pdf') {
      return <DocumentIcon className="w-8 h-8 text-red-500" />;
    } else if (file.type.startsWith('image/')) {
      return file.preview ? (
        <img 
          src={file.preview} 
          alt={file.name}
          className="w-8 h-8 object-cover rounded"
        />
      ) : (
        <DocumentIcon className="w-8 h-8 text-blue-500" />
      );
    }
    return <DocumentIcon className="w-8 h-8 text-gray-500" />;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />;
      case 'uploading':
        return (
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        );
      default:
        return null;
    }
  };

  const dropzoneClasses = `
    relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 cursor-pointer
    ${isDragActive && !isDragReject ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' : ''}
    ${isDragReject ? 'border-red-400 bg-red-50 dark:bg-red-900/20' : ''}
    ${!isDragActive ? 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500' : ''}
    ${uploading || files.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}
  `;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Dropzone */}
      <div {...getRootProps()} className={dropzoneClasses}>
        <input {...getInputProps()} />
        
        <motion.div
          initial={{ scale: 1 }}
          animate={{ scale: isDragActive ? 1.05 : 1 }}
          transition={{ duration: 0.2 }}
          className="space-y-4"
        >
          <CloudUploadIcon className="w-12 h-12 mx-auto text-gray-400" />
          
          {isDragActive ? (
            <div>
              <p className="text-lg font-medium text-blue-600 dark:text-blue-400">
                {isDragReject ? 'Invalid file type' : 'Drop files here'}
              </p>
              <p className="text-sm text-gray-500">
                {isDragReject ? 'Please check file type and size' : 'Release to upload'}
              </p>
            </div>
          ) : (
            <div>
              <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
                Drag & drop files here, or click to select
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Supports PDF, JPG, PNG, TIFF up to {apiUtils.formatFileSize(maxSize)}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Maximum {maxFiles} files ({files.length}/{maxFiles} selected)
              </p>
            </div>
          )}
        </motion.div>
      </div>

      {/* File List */}
      <AnimatePresence>
        {files.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Selected Files ({files.length})
              </h3>
              <button
                onClick={clearAllFiles}
                className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                disabled={uploading}
              >
                Clear All
              </button>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {files.map((file) => (
                <motion.div
                  key={file.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                >
                  {/* File Icon */}
                  <div className="flex-shrink-0">
                    {getFileIcon(file)}
                  </div>

                  {/* File Info */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {file.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {apiUtils.formatFileSize(file.size)}
                    </p>
                    
                    {/* Progress Bar */}
                    {file.status === 'uploading' && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress[file.id] || 0}%` }}
                          />
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {uploadProgress[file.id] || 0}% uploaded
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Status & Actions */}
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(file.status)}
                    
                    <button
                      onClick={() => removeFile(file.id)}
                      className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                      disabled={file.status === 'uploading'}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload Summary */}
      {files.length > 0 && (
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Total size: {apiUtils.formatFileSize(files.reduce((sum, file) => sum + file.size, 0))}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
