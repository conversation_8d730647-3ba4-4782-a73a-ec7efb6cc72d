/**
 * FinScanAI Document Model
 * Handles uploaded documents and their metadata
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const path = require('path');

const Document = sequelize.define('Document', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false
  },
  
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  
  // File Information
  originalName: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      len: {
        args: [1, 255],
        msg: 'Original filename must be between 1 and 255 characters'
      }
    }
  },
  
  fileName: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  
  filePath: {
    type: DataTypes.STRING,
    allowNull: false
  },
  
  fileSize: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: {
        args: [1],
        msg: 'File size must be greater than 0'
      },
      max: {
        args: [50 * 1024 * 1024], // 50MB
        msg: 'File size cannot exceed 50MB'
      }
    }
  },
  
  mimeType: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isIn: {
        args: [['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/tiff']],
        msg: 'File type must be PDF, JPG, PNG, or TIFF'
      }
    }
  },
  
  fileExtension: {
    type: DataTypes.STRING,
    allowNull: false
  },
  
  // Processing Information
  status: {
    type: DataTypes.ENUM(
      'uploaded',
      'queued',
      'processing',
      'completed',
      'failed',
      'cancelled'
    ),
    defaultValue: 'uploaded',
    allowNull: false
  },
  
  processingStartedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  processingCompletedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  
  processingDuration: {
    type: DataTypes.INTEGER, // in milliseconds
    allowNull: true
  },
  
  // Document Metadata
  pageCount: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: {
        args: [1],
        msg: 'Page count must be at least 1'
      }
    }
  },
  
  language: {
    type: DataTypes.STRING,
    defaultValue: 'en',
    allowNull: false,
    validate: {
      isIn: {
        args: [['en', 'vi', 'en+vi']],
        msg: 'Language must be en, vi, or en+vi'
      }
    }
  },
  
  documentType: {
    type: DataTypes.ENUM(
      'financial_statement',
      'invoice',
      'receipt',
      'bank_statement',
      'tax_document',
      'other'
    ),
    defaultValue: 'other',
    allowNull: false
  },
  
  // Processing Configuration
  ocrEngine: {
    type: DataTypes.ENUM('tesseract', 'google_vision', 'hybrid'),
    defaultValue: 'tesseract',
    allowNull: false
  },
  
  processingOptions: {
    type: DataTypes.JSON,
    defaultValue: {
      enhanceImage: true,
      detectTables: true,
      extractNumbers: true,
      confidenceThreshold: 60
    }
  },
  
  // Error Information
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  errorCode: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  retryCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  
  // Metadata
  tags: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  isPublic: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'documents',
  timestamps: true,
  paranoid: true, // Soft delete
  indexes: [
    {
      fields: ['userId']
    },
    {
      fields: ['status']
    },
    {
      fields: ['documentType']
    },
    {
      fields: ['createdAt']
    },
    {
      fields: ['fileName']
    },
    {
      fields: ['expiresAt']
    }
  ],
  hooks: {
    beforeCreate: (document) => {
      // Set file extension from original name
      document.fileExtension = path.extname(document.originalName).toLowerCase();
      
      // Set expiration date (30 days from creation)
      if (!document.expiresAt) {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 30);
        document.expiresAt = expirationDate;
      }
    }
  }
});

/**
 * Instance Methods
 */

// Get processing duration in human readable format
Document.prototype.getProcessingDuration = function() {
  if (!this.processingDuration) return null;
  
  const seconds = Math.floor(this.processingDuration / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
};

// Check if document is expired
Document.prototype.isExpired = function() {
  return this.expiresAt && new Date() > this.expiresAt;
};

// Get file URL
Document.prototype.getFileUrl = function() {
  return `/api/documents/${this.id}/download`;
};

// Update processing status
Document.prototype.updateStatus = async function(status, options = {}) {
  const updates = { status };
  
  if (status === 'processing') {
    updates.processingStartedAt = new Date();
  } else if (status === 'completed' || status === 'failed') {
    updates.processingCompletedAt = new Date();
    
    if (this.processingStartedAt) {
      updates.processingDuration = Date.now() - this.processingStartedAt.getTime();
    }
  }
  
  if (status === 'failed' && options.error) {
    updates.errorMessage = options.error.message;
    updates.errorCode = options.error.code;
    updates.retryCount = this.retryCount + 1;
  }
  
  return this.update(updates);
};

// Get file size in human readable format
Document.prototype.getFileSizeFormatted = function() {
  const bytes = this.fileSize;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  
  if (bytes === 0) return '0 Bytes';
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

/**
 * Class Methods
 */

// Find documents by user
Document.findByUser = function(userId, options = {}) {
  return this.findAll({
    where: { userId },
    order: [['createdAt', 'DESC']],
    ...options
  });
};

// Find documents by status
Document.findByStatus = function(status, options = {}) {
  return this.findAll({
    where: { status },
    order: [['createdAt', 'ASC']],
    ...options
  });
};

// Clean up expired documents
Document.cleanupExpired = async function() {
  const expiredDocs = await this.findAll({
    where: {
      expiresAt: {
        [sequelize.Sequelize.Op.lt]: new Date()
      }
    }
  });
  
  // TODO: Delete actual files from storage
  const deletedCount = await this.destroy({
    where: {
      expiresAt: {
        [sequelize.Sequelize.Op.lt]: new Date()
      }
    },
    force: true // Hard delete
  });
  
  return { deletedCount, expiredDocs };
};

module.exports = Document;
