"""
FinScanAI OCR Service Configuration
Centralized settings management for the OCR processing service
"""

import os
from pathlib import Path
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application Configuration
    app_name: str = Field(default="FinScanAI OCR Service", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="OCR_SERVICE_HOST")
    port: int = Field(default=8000, env="OCR_SERVICE_PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # File Processing Configuration
    max_file_size: int = Field(default=10485760, env="MAX_FILE_SIZE")  # 10MB
    allowed_extensions: List[str] = Field(
        default=["pdf", "jpg", "jpeg", "png", "tiff", "bmp"],
        env="ALLOWED_EXTENSIONS"
    )
    upload_dir: str = Field(default="./uploads", env="UPLOAD_DIR")
    temp_dir: str = Field(default="./temp", env="TEMP_DIR")
    output_dir: str = Field(default="./output", env="OUTPUT_DIR")
    
    # Tesseract Configuration
    tesseract_path: str = Field(default="/usr/bin/tesseract", env="TESSERACT_PATH")
    tesseract_data_path: Optional[str] = Field(default=None, env="TESSERACT_DATA_PATH")
    supported_languages: str = Field(default="eng+vie", env="SUPPORTED_LANGUAGES")
    ocr_confidence_threshold: int = Field(default=60, env="OCR_CONFIDENCE_THRESHOLD")
    
    # Image Processing Configuration
    image_max_width: int = Field(default=2048, env="IMAGE_MAX_WIDTH")
    image_max_height: int = Field(default=2048, env="IMAGE_MAX_HEIGHT")
    image_dpi: int = Field(default=300, env="IMAGE_DPI")
    enhance_image: bool = Field(default=True, env="ENHANCE_IMAGE")
    
    # Processing Configuration
    max_concurrent_jobs: int = Field(default=10, env="MAX_CONCURRENT_JOBS")
    processing_timeout: int = Field(default=300, env="PROCESSING_TIMEOUT")  # 5 minutes
    retry_attempts: int = Field(default=3, env="RETRY_ATTEMPTS")
    
    # Google Vision API Configuration (Optional)
    google_vision_api_key: Optional[str] = Field(default=None, env="GOOGLE_VISION_API_KEY")
    google_vision_enabled: bool = Field(default=False, env="GOOGLE_VISION_ENABLED")
    
    # Database Configuration (Optional)
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # Redis Configuration (Optional)
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./logs/ocr_service.log", env="LOG_FILE")
    log_rotation: str = Field(default="1 day", env="LOG_ROTATION")
    log_retention: str = Field(default="30 days", env="LOG_RETENTION")
    
    # Security Configuration
    api_key: Optional[str] = Field(default=None, env="OCR_API_KEY")
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:5000"],
        env="CORS_ORIGINS"
    )
    
    # Performance Configuration
    enable_gpu: bool = Field(default=False, env="ENABLE_GPU")
    batch_size: int = Field(default=1, env="BATCH_SIZE")
    memory_limit: Optional[str] = Field(default=None, env="MEMORY_LIMIT")
    
    # Feature Flags
    enable_table_detection: bool = Field(default=True, env="ENABLE_TABLE_DETECTION")
    enable_number_extraction: bool = Field(default=True, env="ENABLE_NUMBER_EXTRACTION")
    enable_date_extraction: bool = Field(default=True, env="ENABLE_DATE_EXTRACTION")
    enable_entity_recognition: bool = Field(default=True, env="ENABLE_ENTITY_RECOGNITION")
    
    # Custom Algorithm Configuration
    table_detection_confidence: float = Field(default=0.7, env="TABLE_DETECTION_CONFIDENCE")
    number_extraction_confidence: float = Field(default=0.8, env="NUMBER_EXTRACTION_CONFIDENCE")
    financial_validation_enabled: bool = Field(default=True, env="FINANCIAL_VALIDATION_ENABLED")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    @validator("supported_languages")
    def validate_languages(cls, v):
        """Validate supported languages format"""
        valid_languages = ["eng", "vie", "chi_sim", "chi_tra", "jpn", "kor"]
        languages = v.replace("+", " ").split()
        
        for lang in languages:
            if lang not in valid_languages:
                raise ValueError(f"Unsupported language: {lang}")
        
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Invalid log level: {v}")
        return v.upper()
    
    @validator("max_file_size")
    def validate_file_size(cls, v):
        """Validate maximum file size"""
        if v <= 0:
            raise ValueError("Max file size must be positive")
        if v > 100 * 1024 * 1024:  # 100MB
            raise ValueError("Max file size cannot exceed 100MB")
        return v
    
    def create_directories(self):
        """Create required directories if they don't exist"""
        directories = [
            self.upload_dir,
            self.temp_dir,
            self.output_dir,
            Path(self.log_file).parent
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_tesseract_config(self) -> str:
        """Get Tesseract configuration string"""
        config = f"--oem 3 --psm 6 -l {self.supported_languages}"
        
        if self.tesseract_data_path:
            config += f" --tessdata-dir {self.tesseract_data_path}"
        
        return config
    
    def get_processing_config(self) -> dict:
        """Get processing configuration dictionary"""
        return {
            "enhance_image": self.enhance_image,
            "detect_tables": self.enable_table_detection,
            "extract_numbers": self.enable_number_extraction,
            "extract_dates": self.enable_date_extraction,
            "recognize_entities": self.enable_entity_recognition,
            "confidence_threshold": self.ocr_confidence_threshold,
            "table_confidence": self.table_detection_confidence,
            "number_confidence": self.number_extraction_confidence,
            "validate_financial": self.financial_validation_enabled
        }
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment.lower() == "development"


# Global settings instance
settings = Settings()

# Create required directories on import
settings.create_directories()


def get_settings() -> Settings:
    """Get application settings instance"""
    return settings


# Export commonly used configurations
TESSERACT_CONFIG = settings.get_tesseract_config()
PROCESSING_CONFIG = settings.get_processing_config()
SUPPORTED_LANGUAGES = settings.supported_languages.split("+")
MAX_FILE_SIZE = settings.max_file_size
ALLOWED_EXTENSIONS = settings.allowed_extensions
