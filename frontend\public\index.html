<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1f2937" />
    <meta name="description" content="FinScanAI - Advanced Financial Report OCR Solution. Extract, process, and analyze financial data from documents with AI-powered accuracy." />
    <meta name="keywords" content="OCR, Financial Reports, Document Processing, AI, Machine Learning, Data Extraction" />
    <meta name="author" content="FinScanAI Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://finscannai.com/" />
    <meta property="og:title" content="FinScanAI - Financial Report OCR Solution" />
    <meta property="og:description" content="Advanced AI-powered OCR for financial documents. Extract and analyze financial data with precision." />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://finscannai.com/" />
    <meta property="twitter:title" content="FinScanAI - Financial Report OCR Solution" />
    <meta property="twitter:description" content="Advanced AI-powered OCR for financial documents. Extract and analyze financial data with precision." />
    <meta property="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS Variables -->
    <style>
      :root {
        --primary-color: #3b82f6;
        --primary-dark: #1d4ed8;
        --secondary-color: #10b981;
        --accent-color: #f59e0b;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --background: #ffffff;
        --surface: #f9fafb;
        --border: #e5e7eb;
        --error: #ef4444;
        --warning: #f59e0b;
        --success: #10b981;
        --info: #3b82f6;
      }
      
      [data-theme="dark"] {
        --primary-color: #60a5fa;
        --primary-dark: #3b82f6;
        --secondary-color: #34d399;
        --accent-color: #fbbf24;
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --background: #111827;
        --surface: #1f2937;
        --border: #374151;
        --error: #f87171;
        --warning: #fbbf24;
        --success: #34d399;
        --info: #60a5fa;
      }
      
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: var(--background);
        color: var(--text-primary);
        transition: background-color 0.3s ease, color 0.3s ease;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Loading spinner */
      .loading-spinner {
        border: 3px solid var(--border);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: var(--surface);
      }
      
      ::-webkit-scrollbar-thumb {
        background: var(--border);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: var(--text-secondary);
      }
      
      /* Focus styles */
      .focus-ring:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }
      
      /* Animation utilities */
      .fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      
      .slide-up {
        animation: slideUp 0.3s ease-out;
      }
      
      @keyframes slideUp {
        from { transform: translateY(20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
      
      /* Print styles */
      @media print {
        body {
          background: white !important;
          color: black !important;
        }
        
        .no-print {
          display: none !important;
        }
      }
      
      /* Mobile optimizations */
      @media (max-width: 768px) {
        body {
          font-size: 14px;
        }
      }
    </style>
    
    <title>FinScanAI - Financial Report OCR Solution</title>
  </head>
  <body>
    <noscript>
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f9fafb;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: Inter, sans-serif;
      ">
        <div style="text-align: center; max-width: 400px; padding: 2rem;">
          <h1 style="color: #1f2937; margin-bottom: 1rem;">JavaScript Required</h1>
          <p style="color: #6b7280; margin-bottom: 1.5rem;">
            FinScanAI requires JavaScript to function properly. Please enable JavaScript in your browser settings and reload the page.
          </p>
          <button onclick="window.location.reload()" style="
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
          ">
            Reload Page
          </button>
        </div>
      </div>
    </noscript>
    
    <!-- Loading screen -->
    <div id="initial-loading" style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9998;
      transition: opacity 0.5s ease-out;
    ">
      <div style="text-align: center; color: white;">
        <div class="loading-spinner" style="margin: 0 auto 1rem; border-color: rgba(255,255,255,0.3); border-top-color: white;"></div>
        <h2 style="margin: 0; font-weight: 600;">Loading FinScanAI...</h2>
        <p style="margin: 0.5rem 0 0; opacity: 0.8;">Preparing your financial OCR solution</p>
      </div>
    </div>
    
    <!-- Main React App Container -->
    <div id="root"></div>
    
    <!-- Hide loading screen when React loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('initial-loading');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
