/**
 * FinScanAI Processing Result Model
 * Stores OCR processing results and extracted data
 */

const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProcessingResult = sequelize.define('ProcessingResult', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
    allowNull: false
  },
  
  documentId: {
    type: DataTypes.UUID,
    allowNull: false,
    unique: true,
    references: {
      model: 'documents',
      key: 'id'
    }
  },
  
  // Raw OCR Results
  rawText: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  
  // Structured Data
  extractedData: {
    type: DataTypes.JSON,
    defaultValue: {},
    allowNull: false
  },
  
  // Financial Data
  financialData: {
    type: DataTypes.JSON,
    defaultValue: {
      tables: [],
      numbers: [],
      currencies: [],
      dates: [],
      entities: []
    },
    allowNull: false
  },
  
  // Table Detection Results
  tables: {
    type: DataTypes.JSON,
    defaultValue: [],
    allowNull: false
  },
  
  // Confidence Scores
  confidence: {
    type: DataTypes.JSON,
    defaultValue: {
      overall: 0,
      text: 0,
      tables: 0,
      numbers: 0
    },
    allowNull: false
  },
  
  // Processing Metadata
  processingEngine: {
    type: DataTypes.STRING,
    allowNull: false
  },
  
  processingVersion: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  processingTime: {
    type: DataTypes.INTEGER, // milliseconds
    allowNull: true
  },
  
  // Quality Metrics
  qualityScore: {
    type: DataTypes.FLOAT,
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    }
  },
  
  // Text Statistics
  textStats: {
    type: DataTypes.JSON,
    defaultValue: {
      totalCharacters: 0,
      totalWords: 0,
      totalLines: 0,
      languageDetected: null
    },
    allowNull: false
  },
  
  // Validation Results
  validationResults: {
    type: DataTypes.JSON,
    defaultValue: {
      isValid: false,
      errors: [],
      warnings: []
    },
    allowNull: false
  },
  
  // Export Information
  exports: {
    type: DataTypes.JSON,
    defaultValue: [],
    allowNull: false
  },
  
  // Processing Errors
  errors: {
    type: DataTypes.JSON,
    defaultValue: [],
    allowNull: false
  },
  
  warnings: {
    type: DataTypes.JSON,
    defaultValue: [],
    allowNull: false
  }
}, {
  tableName: 'processing_results',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['documentId']
    },
    {
      fields: ['processingEngine']
    },
    {
      fields: ['qualityScore']
    },
    {
      fields: ['createdAt']
    }
  ]
});

/**
 * Instance Methods
 */

// Get overall confidence score
ProcessingResult.prototype.getOverallConfidence = function() {
  const { overall, text, tables, numbers } = this.confidence;
  return overall || Math.round((text + tables + numbers) / 3);
};

// Check if result is high quality
ProcessingResult.prototype.isHighQuality = function() {
  return this.getOverallConfidence() >= 80 && this.qualityScore >= 70;
};

// Get extracted numbers with confidence
ProcessingResult.prototype.getFinancialNumbers = function() {
  return this.financialData.numbers.filter(num => num.confidence >= 60);
};

// Get detected tables
ProcessingResult.prototype.getTables = function() {
  return this.tables.filter(table => table.confidence >= 50);
};

// Add export record
ProcessingResult.prototype.addExport = async function(format, filePath) {
  const exports = [...this.exports];
  exports.push({
    format,
    filePath,
    exportedAt: new Date(),
    id: require('uuid').v4()
  });
  
  return this.update({ exports });
};

// Get text statistics
ProcessingResult.prototype.getTextStatistics = function() {
  if (!this.rawText) return this.textStats;
  
  const text = this.rawText;
  const stats = {
    totalCharacters: text.length,
    totalWords: text.split(/\s+/).filter(word => word.length > 0).length,
    totalLines: text.split('\n').length,
    languageDetected: this.detectLanguage(text)
  };
  
  return stats;
};

// Simple language detection
ProcessingResult.prototype.detectLanguage = function(text) {
  const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
  const vietnameseRatio = (text.match(vietnameseChars) || []).length / text.length;
  
  if (vietnameseRatio > 0.1) {
    return 'vi';
  }
  return 'en';
};

// Validate financial data
ProcessingResult.prototype.validateFinancialData = function() {
  const errors = [];
  const warnings = [];
  
  // Check for required financial elements
  if (this.financialData.numbers.length === 0) {
    warnings.push('No numerical data detected');
  }
  
  if (this.tables.length === 0) {
    warnings.push('No tables detected');
  }
  
  // Check confidence levels
  const lowConfidenceItems = this.financialData.numbers.filter(num => num.confidence < 60);
  if (lowConfidenceItems.length > 0) {
    warnings.push(`${lowConfidenceItems.length} numbers have low confidence`);
  }
  
  // Validate number formats
  const invalidNumbers = this.financialData.numbers.filter(num => {
    return isNaN(parseFloat(num.value.replace(/[,\s]/g, '')));
  });
  
  if (invalidNumbers.length > 0) {
    errors.push(`${invalidNumbers.length} invalid number formats detected`);
  }
  
  const isValid = errors.length === 0;
  
  return {
    isValid,
    errors,
    warnings
  };
};

/**
 * Class Methods
 */

// Find by document ID
ProcessingResult.findByDocumentId = function(documentId) {
  return this.findOne({ where: { documentId } });
};

// Get statistics for user
ProcessingResult.getStatistics = async function(userId = null) {
  const whereClause = userId ? {
    include: [{
      model: require('./Document'),
      as: 'document',
      where: { userId }
    }]
  } : {};
  
  const results = await this.findAll(whereClause);
  
  return {
    totalProcessed: results.length,
    averageConfidence: results.reduce((sum, r) => sum + r.getOverallConfidence(), 0) / results.length || 0,
    highQualityCount: results.filter(r => r.isHighQuality()).length,
    averageProcessingTime: results.reduce((sum, r) => sum + (r.processingTime || 0), 0) / results.length || 0
  };
};

module.exports = ProcessingResult;
