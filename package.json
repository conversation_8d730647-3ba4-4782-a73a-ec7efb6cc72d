{"name": "finscannai-financial-report-ocr", "version": "1.0.0", "description": "Comprehensive Financial Report OCR Solution with React frontend, Node.js backend, and Python OCR service", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:ocr\"", "dev:frontend": "cd frontend && npm start", "dev:backend": "cd backend && npm run dev", "dev:ocr": "cd ocr-service && python main.py", "build": "cd frontend && npm run build", "test": "concurrently \"npm run test:backend\" \"npm run test:frontend\" \"npm run test:ocr\"", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "test:ocr": "cd ocr-service && python -m pytest", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../ocr-service && pip install -r requirements.txt", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "lint": "concurrently \"cd frontend && npm run lint\" \"cd backend && npm run lint\"", "format": "concurrently \"cd frontend && npm run format\" \"cd backend && npm run format\""}, "keywords": ["ocr", "financial", "document-processing", "react", "nodejs", "python", "tesseract", "ai", "fintech"], "author": "HectorTa1989", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/HectorTa1989/FinScanAI_Financial_Report_OCR.git"}, "bugs": {"url": "https://github.com/HectorTa1989/FinScanAI_Financial_Report_OCR/issues"}, "homepage": "https://github.com/HectorTa1989/FinScanAI_Financial_Report_OCR#readme", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}