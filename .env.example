# FinScanAI Environment Configuration Template
# Copy this file to .env and update with your actual values

# ===========================================
# GLOBAL CONFIGURATION
# ===========================================
NODE_ENV=development
APP_NAME=FinScanAI
APP_VERSION=1.0.0

# ===========================================
# BACKEND CONFIGURATION
# ===========================================
# Server Configuration
BACKEND_PORT=5000
BACKEND_HOST=localhost
API_BASE_URL=http://localhost:5000/api

# Database Configuration
DATABASE_TYPE=sqlite
DATABASE_URL=sqlite:./database/finscannai.db
# For PostgreSQL production:
# DATABASE_URL=postgresql://username:password@localhost:5432/finscannai

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,tiff
UPLOAD_DIR=./uploads
TEMP_DIR=./temp

# ===========================================
# OCR SERVICE CONFIGURATION
# ===========================================
OCR_SERVICE_PORT=8000
OCR_SERVICE_HOST=localhost
OCR_SERVICE_URL=http://localhost:8000

# Tesseract Configuration
TESSERACT_PATH=/usr/bin/tesseract
TESSERACT_DATA_PATH=/usr/share/tesseract-ocr/4.00/tessdata
SUPPORTED_LANGUAGES=eng+vie
OCR_CONFIDENCE_THRESHOLD=60

# Google Vision API (Optional)
GOOGLE_VISION_API_KEY=your-google-vision-api-key
GOOGLE_VISION_ENABLED=false

# Processing Configuration
MAX_CONCURRENT_JOBS=10
PROCESSING_TIMEOUT=300000
IMAGE_MAX_WIDTH=2048
IMAGE_MAX_HEIGHT=2048

# ===========================================
# FRONTEND CONFIGURATION
# ===========================================
FRONTEND_PORT=3000
FRONTEND_HOST=localhost
REACT_APP_API_URL=http://localhost:5000/api
REACT_APP_OCR_SERVICE_URL=http://localhost:8000
REACT_APP_MAX_FILE_SIZE=10485760

# ===========================================
# DOCKER CONFIGURATION
# ===========================================
COMPOSE_PROJECT_NAME=finscannai
DOCKER_REGISTRY=
DOCKER_TAG=latest

# Database (Docker)
POSTGRES_DB=finscannai
POSTGRES_USER=finscannai_user
POSTGRES_PASSWORD=secure_password_change_this
POSTGRES_PORT=5432

# Redis (Optional - for job queuing)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ===========================================
# LOGGING CONFIGURATION
# ===========================================
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ===========================================
# SECURITY CONFIGURATION
# ===========================================
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
HELMET_ENABLED=true

# ===========================================
# MONITORING & ANALYTICS
# ===========================================
ENABLE_METRICS=false
METRICS_PORT=9090
HEALTH_CHECK_ENDPOINT=/health

# ===========================================
# EMAIL CONFIGURATION (Optional)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# ===========================================
# CLOUD STORAGE (Optional)
# ===========================================
CLOUD_STORAGE_PROVIDER=local
# For AWS S3:
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=finscannai-documents

# ===========================================
# DEVELOPMENT TOOLS
# ===========================================
DEBUG=finscannai:*
ENABLE_SWAGGER=true
ENABLE_CORS_DEBUG=false
