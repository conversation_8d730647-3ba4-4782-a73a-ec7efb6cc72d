"""
FinScanAI Custom Table Detection Algorithm
Advanced table detection specifically designed for financial documents
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from pathlib import Path

from ..config.settings import settings

logger = logging.getLogger(__name__)


class TableDetector:
    """Custom table detection algorithm for financial documents"""
    
    def __init__(self):
        self.min_table_area = 1000  # Minimum area for a table
        self.line_thickness_threshold = 3
        self.confidence_threshold = settings.table_detection_confidence
        
    def detect_tables(self, image_path: str) -> List[Dict]:
        """
        Detect tables in financial documents
        
        Args:
            image_path: Path to the image file
            
        Returns:
            List of detected tables with coordinates and confidence
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Detect horizontal and vertical lines
            horizontal_lines = self._detect_horizontal_lines(gray)
            vertical_lines = self._detect_vertical_lines(gray)
            
            # Find table intersections
            intersections = self._find_line_intersections(horizontal_lines, vertical_lines)
            
            # Group intersections into table candidates
            table_candidates = self._group_intersections_into_tables(intersections, gray.shape)
            
            # Validate and score table candidates
            validated_tables = []
            for candidate in table_candidates:
                table_info = self._validate_table_candidate(candidate, gray)
                if table_info and table_info['confidence'] >= self.confidence_threshold:
                    validated_tables.append(table_info)
            
            # Sort by confidence
            validated_tables.sort(key=lambda x: x['confidence'], reverse=True)
            
            logger.info(f"Detected {len(validated_tables)} tables in {image_path}")
            return validated_tables
            
        except Exception as e:
            logger.error(f"Error detecting tables in {image_path}: {str(e)}")
            return []
    
    def _detect_horizontal_lines(self, gray: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect horizontal lines in the image"""
        # Create horizontal kernel
        horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
        
        # Apply morphological operations
        horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)
        
        # Apply threshold
        _, horizontal_lines = cv2.threshold(horizontal_lines, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Find contours
        contours, _ = cv2.findContours(horizontal_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        lines = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if w > 50 and h < 10:  # Filter for horizontal lines
                lines.append((x, y, x + w, y + h))
        
        return lines
    
    def _detect_vertical_lines(self, gray: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """Detect vertical lines in the image"""
        # Create vertical kernel
        vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
        
        # Apply morphological operations
        vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)
        
        # Apply threshold
        _, vertical_lines = cv2.threshold(vertical_lines, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Find contours
        contours, _ = cv2.findContours(vertical_lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        lines = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            if h > 50 and w < 10:  # Filter for vertical lines
                lines.append((x, y, x + w, y + h))
        
        return lines
    
    def _find_line_intersections(self, horizontal_lines: List, vertical_lines: List) -> List[Tuple[int, int]]:
        """Find intersections between horizontal and vertical lines"""
        intersections = []
        
        for h_line in horizontal_lines:
            hx1, hy1, hx2, hy2 = h_line
            for v_line in vertical_lines:
                vx1, vy1, vx2, vy2 = v_line
                
                # Check if lines intersect
                if (hx1 <= vx1 <= hx2 or hx1 <= vx2 <= hx2) and \
                   (vy1 <= hy1 <= vy2 or vy1 <= hy2 <= vy2):
                    # Calculate intersection point
                    intersection_x = max(hx1, vx1)
                    intersection_y = max(hy1, vy1)
                    intersections.append((intersection_x, intersection_y))
        
        return intersections
    
    def _group_intersections_into_tables(self, intersections: List, image_shape: Tuple) -> List[Dict]:
        """Group intersections into potential table regions"""
        if len(intersections) < 4:
            return []
        
        # Sort intersections
        intersections = sorted(intersections)
        
        table_candidates = []
        
        # Use clustering approach to group nearby intersections
        clusters = self._cluster_intersections(intersections)
        
        for cluster in clusters:
            if len(cluster) >= 4:  # Minimum 4 points for a table
                # Find bounding rectangle
                x_coords = [point[0] for point in cluster]
                y_coords = [point[1] for point in cluster]
                
                min_x, max_x = min(x_coords), max(x_coords)
                min_y, max_y = min(y_coords), max(y_coords)
                
                width = max_x - min_x
                height = max_y - min_y
                
                if width > 100 and height > 50:  # Minimum table size
                    table_candidates.append({
                        'bbox': (min_x, min_y, width, height),
                        'intersections': cluster,
                        'intersection_count': len(cluster)
                    })
        
        return table_candidates
    
    def _cluster_intersections(self, intersections: List, distance_threshold: int = 50) -> List[List]:
        """Cluster nearby intersections"""
        if not intersections:
            return []
        
        clusters = []
        used = set()
        
        for i, point in enumerate(intersections):
            if i in used:
                continue
            
            cluster = [point]
            used.add(i)
            
            # Find nearby points
            for j, other_point in enumerate(intersections):
                if j in used:
                    continue
                
                distance = np.sqrt((point[0] - other_point[0])**2 + (point[1] - other_point[1])**2)
                if distance <= distance_threshold:
                    cluster.append(other_point)
                    used.add(j)
            
            if len(cluster) >= 2:
                clusters.append(cluster)
        
        return clusters
    
    def _validate_table_candidate(self, candidate: Dict, gray: np.ndarray) -> Optional[Dict]:
        """Validate and score a table candidate"""
        bbox = candidate['bbox']
        x, y, w, h = bbox
        
        # Extract table region
        table_region = gray[y:y+h, x:x+w]
        
        if table_region.size == 0:
            return None
        
        # Calculate various features for scoring
        features = self._extract_table_features(table_region, candidate)
        
        # Calculate confidence score
        confidence = self._calculate_table_confidence(features)
        
        if confidence < 0.3:  # Minimum confidence threshold
            return None
        
        # Estimate table structure
        structure = self._estimate_table_structure(table_region, candidate)
        
        return {
            'bbox': bbox,
            'confidence': confidence,
            'features': features,
            'structure': structure,
            'intersection_count': candidate['intersection_count']
        }
    
    def _extract_table_features(self, table_region: np.ndarray, candidate: Dict) -> Dict:
        """Extract features from table region for validation"""
        features = {}
        
        # Basic geometric features
        h, w = table_region.shape
        features['aspect_ratio'] = w / h if h > 0 else 0
        features['area'] = w * h
        
        # Line density features
        horizontal_lines = self._count_horizontal_lines_in_region(table_region)
        vertical_lines = self._count_vertical_lines_in_region(table_region)
        
        features['horizontal_line_density'] = horizontal_lines / h if h > 0 else 0
        features['vertical_line_density'] = vertical_lines / w if w > 0 else 0
        features['line_balance'] = min(horizontal_lines, vertical_lines) / max(horizontal_lines, vertical_lines) if max(horizontal_lines, vertical_lines) > 0 else 0
        
        # Text density (approximate)
        non_white_pixels = np.sum(table_region < 240)
        features['text_density'] = non_white_pixels / (w * h) if w * h > 0 else 0
        
        # Intersection features
        features['intersection_density'] = candidate['intersection_count'] / (w * h) * 10000  # Per 10k pixels
        
        return features
    
    def _count_horizontal_lines_in_region(self, region: np.ndarray) -> int:
        """Count horizontal lines in a region"""
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (20, 1))
        lines = cv2.morphologyEx(region, cv2.MORPH_OPEN, kernel)
        contours, _ = cv2.findContours(lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return len([c for c in contours if cv2.boundingRect(c)[2] > 15])
    
    def _count_vertical_lines_in_region(self, region: np.ndarray) -> int:
        """Count vertical lines in a region"""
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 20))
        lines = cv2.morphologyEx(region, cv2.MORPH_OPEN, kernel)
        contours, _ = cv2.findContours(lines, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        return len([c for c in contours if cv2.boundingRect(c)[3] > 15])
    
    def _calculate_table_confidence(self, features: Dict) -> float:
        """Calculate confidence score for table candidate"""
        confidence = 0.0
        
        # Area score (prefer medium-sized tables)
        area = features['area']
        if 5000 <= area <= 100000:
            confidence += 0.3
        elif 1000 <= area <= 200000:
            confidence += 0.2
        
        # Aspect ratio score (prefer rectangular tables)
        aspect_ratio = features['aspect_ratio']
        if 0.5 <= aspect_ratio <= 3.0:
            confidence += 0.2
        
        # Line density score
        h_density = features['horizontal_line_density']
        v_density = features['vertical_line_density']
        if h_density > 0.01 and v_density > 0.01:
            confidence += 0.3
        
        # Line balance score
        line_balance = features['line_balance']
        confidence += line_balance * 0.2
        
        # Intersection density score
        intersection_density = features['intersection_density']
        if 0.1 <= intersection_density <= 5.0:
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def _estimate_table_structure(self, table_region: np.ndarray, candidate: Dict) -> Dict:
        """Estimate table structure (rows, columns)"""
        h, w = table_region.shape
        
        # Estimate rows by counting horizontal lines
        horizontal_lines = self._count_horizontal_lines_in_region(table_region)
        estimated_rows = max(1, horizontal_lines - 1)
        
        # Estimate columns by counting vertical lines
        vertical_lines = self._count_vertical_lines_in_region(table_region)
        estimated_cols = max(1, vertical_lines - 1)
        
        return {
            'estimated_rows': estimated_rows,
            'estimated_columns': estimated_cols,
            'cell_count': estimated_rows * estimated_cols
        }


# Global table detector instance
table_detector = TableDetector()


def detect_tables(image_path: str) -> List[Dict]:
    """Convenience function for table detection"""
    return table_detector.detect_tables(image_path)
