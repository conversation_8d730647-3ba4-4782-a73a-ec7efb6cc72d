/**
 * FinScanAI Authentication Middleware
 * Handles JWT token validation and user authentication
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config/environment');

/**
 * Generate JWT token for user
 * @param {Object} user - User object
 * @returns {string} JWT token
 */
const generateToken = (user) => {
  const payload = {
    id: user.id,
    email: user.email,
    role: user.role
  };
  
  return jwt.sign(payload, config.auth.jwtSecret, {
    expiresIn: config.auth.jwtExpiresIn,
    issuer: config.app.name,
    audience: config.app.name
  });
};

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @returns {Object} Decoded token payload
 */
const verifyToken = (token) => {
  return jwt.verify(token, config.auth.jwtSecret, {
    issuer: config.app.name,
    audience: config.app.name
  });
};

/**
 * Extract token from request headers
 * @param {Object} req - Express request object
 * @returns {string|null} Token or null
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (optional)
  if (req.cookies && req.cookies.token) {
    return req.cookies.token;
  }
  
  return null;
};

/**
 * Authentication middleware
 * Validates JWT token and attaches user to request
 */
const authenticate = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return res.status(401).json({
        error: 'Authentication Required',
        message: 'No authentication token provided'
      });
    }
    
    // Verify token
    const decoded = verifyToken(token);
    
    // Find user in database
    const user = await User.findByPk(decoded.id);
    
    if (!user) {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'User not found'
      });
    }
    
    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        error: 'Account Disabled',
        message: 'Your account has been disabled'
      });
    }
    
    // Check if account is locked
    if (user.isLocked()) {
      return res.status(423).json({
        error: 'Account Locked',
        message: 'Account is temporarily locked due to too many failed login attempts'
      });
    }
    
    // Attach user to request
    req.user = user;
    req.token = token;
    
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Authentication Failed',
        message: 'Invalid authentication token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token Expired',
        message: 'Authentication token has expired'
      });
    }
    
    console.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Authentication Error',
      message: 'Internal authentication error'
    });
  }
};

/**
 * Optional authentication middleware
 * Attaches user to request if token is valid, but doesn't require authentication
 */
const optionalAuth = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findByPk(decoded.id);
      
      if (user && user.isActive && !user.isLocked()) {
        req.user = user;
        req.token = token;
      }
    }
    
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

/**
 * Role-based authorization middleware
 * @param {string|Array} roles - Required role(s)
 * @returns {Function} Middleware function
 */
const authorize = (roles) => {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication Required',
        message: 'You must be logged in to access this resource'
      });
    }
    
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Access Denied',
        message: 'You do not have permission to access this resource'
      });
    }
    
    next();
  };
};

/**
 * Usage limit middleware
 * Checks if user has exceeded their monthly usage limit
 */
const checkUsageLimit = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication Required',
        message: 'You must be logged in to access this resource'
      });
    }
    
    if (!req.user.canProcessDocument()) {
      return res.status(429).json({
        error: 'Usage Limit Exceeded',
        message: 'You have exceeded your monthly document processing limit',
        currentUsage: req.user.monthlyUsage,
        limit: req.user.role === 'premium' ? 500 : 50
      });
    }
    
    next();
  } catch (error) {
    console.error('Usage limit check error:', error);
    return res.status(500).json({
      error: 'Usage Check Error',
      message: 'Unable to verify usage limits'
    });
  }
};

/**
 * Refresh token middleware
 * Generates a new token if the current one is close to expiring
 */
const refreshToken = (req, res, next) => {
  if (req.user && req.token) {
    try {
      const decoded = jwt.decode(req.token);
      const now = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = decoded.exp - now;
      
      // If token expires in less than 1 hour, generate a new one
      if (timeUntilExpiry < 3600) {
        const newToken = generateToken(req.user);
        res.setHeader('X-New-Token', newToken);
      }
    } catch (error) {
      // Ignore refresh errors
    }
  }
  
  next();
};

module.exports = {
  generateToken,
  verifyToken,
  extractToken,
  authenticate,
  optionalAuth,
  authorize,
  checkUsageLimit,
  refreshToken
};
