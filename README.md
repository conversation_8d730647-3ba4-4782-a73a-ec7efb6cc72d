# FinScanAI - Financial Report OCR Solution 🏦📊

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18+-blue.svg)](https://reactjs.org/)
[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org/)

## 🚀 Overview

FinScanAI is a comprehensive Financial Report OCR (Optical Character Recognition) solution that automatically extracts, processes, and analyzes financial data from various document formats. Built with modern technologies and custom algorithms, it provides accurate text recognition for both English and Vietnamese financial documents.

### ✨ Key Features

- **Multi-format Support**: PDF, JPG, PNG, TIFF document processing
- **Dual Language**: English and Vietnamese text recognition
- **Multiple Export Options**: Excel, CSV, JSON formats
- **Custom Financial Algorithms**: Proprietary table detection and data validation
- **Real-time Processing**: Fast OCR with confidence scoring
- **Responsive Web Interface**: Modern React.js frontend
- **RESTful API**: Comprehensive backend with authentication
- **Docker Ready**: Containerized deployment
- **Free Tier Optimized**: Uses Tesseract.js and Google Vision API free tier

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React.js Web App]
        B[File Upload Interface]
        C[Results Dashboard]
        D[Export Controls]
    end
    
    subgraph "API Gateway"
        E[Express.js Server]
        F[Authentication Middleware]
        G[File Upload Handler]
        H[API Routes]
    end
    
    subgraph "Processing Layer"
        I[Python OCR Service]
        J[Tesseract Engine]
        K[OpenCV Preprocessing]
        L[Custom Table Detection]
        M[Financial Data Validator]
    end
    
    subgraph "Data Layer"
        N[SQLite/PostgreSQL]
        O[File Storage]
        P[Processing Queue]
    end
    
    subgraph "External Services"
        Q[Google Vision API]
        R[Tesseract.js]
    end
    
    A --> E
    B --> G
    C --> H
    D --> H
    E --> I
    F --> E
    G --> O
    H --> N
    I --> J
    I --> K
    I --> Q
    I --> R
    J --> L
    K --> L
    L --> M
    M --> N
    I --> P
```

## 🔄 Workflow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Server
    participant O as OCR Service
    participant D as Database
    participant S as Storage
    
    U->>F: Upload Financial Document
    F->>A: POST /api/upload
    A->>S: Store Original File
    A->>O: Process Document Request
    
    O->>O: Image Preprocessing
    O->>O: OCR Text Extraction
    O->>O: Table Detection
    O->>O: Financial Data Validation
    O->>O: Confidence Scoring
    
    O->>D: Store Extracted Data
    O->>A: Processing Complete
    A->>F: Return Results
    F->>U: Display Processed Data
    
    U->>F: Request Export
    F->>A: GET /api/export/{format}
    A->>D: Fetch Processed Data
    A->>A: Generate Export File
    A->>F: Return Download Link
    F->>U: Download File
```

## 📁 Project Structure

```
FinScanAI_Financial_Report_OCR/
├── frontend/                    # React.js Frontend Application
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/          # Reusable UI Components
│   │   │   ├── FileUpload.jsx
│   │   │   ├── ResultsTable.jsx
│   │   │   ├── ExportButtons.jsx
│   │   │   └── LoadingSpinner.jsx
│   │   ├── pages/               # Main Application Pages
│   │   │   ├── Dashboard.jsx
│   │   │   ├── Upload.jsx
│   │   │   └── Results.jsx
│   │   ├── services/            # API Communication
│   │   │   └── api.js
│   │   ├── utils/               # Helper Functions
│   │   │   └── formatters.js
│   │   ├── styles/              # CSS Styling
│   │   │   └── main.css
│   │   ├── App.jsx
│   │   └── index.js
│   ├── package.json
│   └── package-lock.json
├── backend/                     # Node.js/Express Backend
│   ├── src/
│   │   ├── controllers/         # Request Handlers
│   │   │   ├── authController.js
│   │   │   ├── uploadController.js
│   │   │   └── exportController.js
│   │   ├── middleware/          # Custom Middleware
│   │   │   ├── auth.js
│   │   │   ├── upload.js
│   │   │   └── validation.js
│   │   ├── models/              # Database Models
│   │   │   ├── User.js
│   │   │   ├── Document.js
│   │   │   └── ProcessingResult.js
│   │   ├── routes/              # API Routes
│   │   │   ├── auth.js
│   │   │   ├── upload.js
│   │   │   └── export.js
│   │   ├── services/            # Business Logic
│   │   │   ├── ocrService.js
│   │   │   └── exportService.js
│   │   ├── utils/               # Helper Functions
│   │   │   ├── database.js
│   │   │   └── fileHandler.js
│   │   ├── config/              # Configuration
│   │   │   ├── database.js
│   │   │   └── environment.js
│   │   └── app.js
│   ├── package.json
│   └── package-lock.json
├── ocr-service/                 # Python OCR Processing Service
│   ├── src/
│   │   ├── processors/          # OCR Processing Modules
│   │   │   ├── image_preprocessor.py
│   │   │   ├── text_extractor.py
│   │   │   ├── table_detector.py
│   │   │   └── financial_validator.py
│   │   ├── algorithms/          # Custom Algorithms
│   │   │   ├── table_detection.py
│   │   │   ├── confidence_scoring.py
│   │   │   └── data_validation.py
│   │   ├── utils/               # Helper Functions
│   │   │   ├── image_utils.py
│   │   │   └── text_utils.py
│   │   ├── config/              # Configuration
│   │   │   └── settings.py
│   │   └── main.py
│   ├── requirements.txt
│   └── Dockerfile
├── database/                    # Database Scripts
│   ├── migrations/
│   │   ├── 001_create_users.sql
│   │   ├── 002_create_documents.sql
│   │   └── 003_create_results.sql
│   └── seeds/
│       └── sample_data.sql
├── docker/                      # Docker Configuration
│   ├── docker-compose.yml
│   ├── frontend.Dockerfile
│   ├── backend.Dockerfile
│   └── nginx.conf
├── docs/                        # Additional Documentation
│   ├── API.md
│   ├── DEPLOYMENT.md
│   └── CONTRIBUTING.md
├── tests/                       # Test Suites
│   ├── frontend/
│   ├── backend/
│   └── ocr-service/
├── .env.example                 # Environment Variables Template
├── .gitignore                   # Git Ignore Rules
├── docker-compose.yml           # Docker Compose Configuration
├── package.json                 # Root Package Configuration
└── README.md                    # This File
```

## 🛠️ Installation & Setup

### Prerequisites

- **Node.js** 18+ and npm
- **Python** 3.9+ with pip
- **Docker** and Docker Compose (optional)
- **Git** for version control

### Quick Start with Docker

```bash
# Clone the repository
git clone https://github.com/HectorTa1989/FinScanAI_Financial_Report_OCR.git
cd FinScanAI_Financial_Report_OCR

# Start all services with Docker Compose
docker-compose up -d

# Access the application
# Frontend: http://localhost:3000
# Backend API: http://localhost:5000
# OCR Service: http://localhost:8000
```

### Manual Installation

#### 1. Backend Setup

```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm run migrate
npm run dev
```

#### 2. OCR Service Setup

```bash
cd ocr-service
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

#### 3. Frontend Setup

```bash
cd frontend
npm install
npm start
```

## 🚀 Usage Examples

### Basic File Upload

```javascript
// Frontend API call example
const uploadFile = async (file) => {
  const formData = new FormData();
  formData.append('document', file);

  const response = await fetch('/api/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  return response.json();
};
```

### API Endpoints

#### Authentication
```bash
# Register new user
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "securepassword"
}

# Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

#### Document Processing
```bash
# Upload document for processing
POST /api/upload
Content-Type: multipart/form-data
Authorization: Bearer <token>

# Get processing results
GET /api/results/{documentId}
Authorization: Bearer <token>

# Export results
GET /api/export/{documentId}/{format}
Authorization: Bearer <token>
# Formats: excel, csv, json
```

### OCR Service API

```python
# Python client example
import requests

def process_document(file_path):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post(
            'http://localhost:8000/process',
            files=files,
            data={'language': 'en+vi'}
        )
    return response.json()
```

## 🔧 Configuration

### Environment Variables

Create `.env` files in each service directory:

#### Backend (.env)
```env
NODE_ENV=development
PORT=5000
DATABASE_URL=sqlite:./database.db
JWT_SECRET=your-secret-key
GOOGLE_VISION_API_KEY=your-api-key
OCR_SERVICE_URL=http://localhost:8000
```

#### OCR Service (.env)
```env
TESSERACT_PATH=/usr/bin/tesseract
GOOGLE_VISION_API_KEY=your-api-key
SUPPORTED_LANGUAGES=eng+vie
MAX_FILE_SIZE=10MB
```

## 🧪 Testing

```bash
# Run all tests
npm run test

# Backend tests
cd backend && npm test

# Frontend tests
cd frontend && npm test

# OCR service tests
cd ocr-service && python -m pytest
```

## 📊 Performance & Accuracy

- **Processing Speed**: ~2-5 seconds per page
- **Accuracy Rate**: 95%+ for clear financial documents
- **Supported File Sizes**: Up to 10MB per document
- **Concurrent Processing**: Up to 10 documents simultaneously
- **Languages**: English and Vietnamese with 90%+ accuracy

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs/](./docs/)
- **Issues**: [GitHub Issues](https://github.com/HectorTa1989/FinScanAI_Financial_Report_OCR/issues)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) for open-source OCR engine
- [Google Vision API](https://cloud.google.com/vision) for enhanced text recognition
- [OpenCV](https://opencv.org/) for image preprocessing capabilities
